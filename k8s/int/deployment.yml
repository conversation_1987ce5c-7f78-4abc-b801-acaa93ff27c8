apiVersion: apps/v1
kind: Deployment
metadata:
  name: front-int
  namespace: weart
  labels: 
    app: front-int
spec:
  replicas: 1
  selector:
    matchLabels:
      app: front-int
  template:
    metadata:
      labels:
        app: front-int
    spec:
      containers:
        - name: front-int
          image: nexus.satoripop.io:8083/repository/projets/front-int-weart:latest
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: front-int-config
      imagePullSecrets:
        - name: nexus.satoripop.io