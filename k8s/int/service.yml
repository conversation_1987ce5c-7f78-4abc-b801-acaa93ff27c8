apiVersion: v1
kind: Service
metadata:
  name: front-int
  namespace: weart
spec:
  selector:
    app: front-int
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000


---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: front-int
  namespace: weart
  annotations:
    acme.cert-manager.io/http01-edit-in-place: 'true'
    cert-manager.io/cluster-issuer: letsencrypt-dns01-issuer
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/auth-realm: Authentication Required - aaa
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/force-ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '3600'
    nginx.ingress.kubernetes.io/satisfy: any
    nginx.ingress.kubernetes.io/ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/whitelist-source-range: >
      *************/32, *************/32, ************/32, ************/32,
      **********/16

spec:
  tls:
    - hosts:
        - front-int-weart.k8s.satoripop.io
      secretName: front-int-ssl-tls
  rules:
    - host: front-int-weart.k8s.satoripop.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: front-int
                port:
                  number: 80