apiVersion: apps/v1
kind: Deployment
metadata:
  name: front-qa
  namespace: weart
  labels: 
    app: front-qa
spec:
  replicas: 1
  selector:
    matchLabels:
      app: front-qa
  template:
    metadata:
      labels:
        app: front-qa
    spec:
      containers:
        - name: front-qa
          image: nexus.satoripop.io:8083/repository/projets/front-qa-weart:latest
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: front-qa-config
      imagePullSecrets:
        - name: nexus.satoripop.io