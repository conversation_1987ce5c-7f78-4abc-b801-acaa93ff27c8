# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start development server with Turbopack for fast hot reloading
- `npm run build` - Build the application for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint to check code quality and Next.js best practices

### Environment
- Node.js version 18.x or higher required
- Uses npm for package management

## Architecture

### Framework Stack
- **Next.js 15** with App Router (app directory structure)
- **React 19** with TypeScript
- **Tailwind CSS v4** for styling
- **Motion** for animations
- **Recharts** for data visualization
- **React Icons** for iconography

### Project Structure
This is a Next.js App Router application with internationalization support:

- `app/(main)/` - Main application routes with shared layout
- `app/fr/` - French localized routes (mirrors main structure)
- `app/components/` - Reusable React components
- `app/hooks/` - Custom React hooks
- `app/lib/` - Utility functions and server-side logic
- `app/locales/` - Translation files (en.json, fr.json)
- `app/types/` - TypeScript type definitions

### Key Features
- **Internationalization**: Server-side i18n with English/French support using custom translation system
- **API Integration**: Fetches data from `api-back-office.weart.exchange` for artist KPIs and metrics
- **Loading States**: Custom navigation loading animation with `useNavigationLoading` hook
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Data Visualization**: Artist analytics dashboard with charts using Recharts

### Core Components
- `Header` - Navigation with KPI ticker, language switching, and mobile menu
- `Footer` - Site footer with localization
- `LoadingAnimation` - Global loading indicator for page transitions
- `Ticker` - Scrolling KPI display component

### Data Flow
- Server-side data fetching in layout components using `fetch()` with revalidation
- KPI data cached for 1 hour (`revalidate: 3600`)
- Client-side state management for UI interactions (menus, loading states)
- Type-safe API responses using defined TypeScript interfaces

### Styling
- Tailwind CSS v4 with PostCSS integration
- Custom CSS variables for fonts (Geist Sans, Geist Mono)
- Responsive design patterns throughout components
- Uses Tailwind's utility-first approach

### TypeScript Configuration
- Strict mode enabled with path mapping (`@/*` points to root)
- Uses Next.js TypeScript plugin for enhanced IDE support
- Server-only imports enforced where appropriate

### Performance Optimizations
- Next.js Image component with remote pattern configuration
- Turbopack for fast development builds
- Server-side rendering with selective client components
- Lazy loading and code splitting built into Next.js App Router

### Important Notes
- The app fetches live data from WeArt's API - ensure `NEXT_PUBLIC_API_BASE_URL` environment variable is set
- Translation keys are nested objects - access via `dictionary.section.key` pattern
- Custom navigation loading hook provides smooth transitions between routes
- Artist data includes complex nested structures for metrics, indexes, and comparison tables