# WeArt Exchange - Fractional Art Investment Platform

This project is a conceptual replica of WeArt.exchange, a modern web platform built with Next.js and styled with Tailwind CSS. The application allows users to collectively invest in high-value works of art by purchasing shares (fractions).

## 🖼️ Description

WeArt Exchange democratizes access to the art market. Traditionally reserved for an elite, investing in masterpieces becomes accessible to everyone through a fractional ownership system. The platform offers works by renowned artists, secures transactions using blockchain technology, and provides a secondary market for reselling shares.

## ✨ Key Features

- Artworks Catalog: Browse a selection of "Blue-Chip" artworks with detailed information (artist, history, price).
- Fractional Purchase: Buy shares of an artwork to become a co-owner.
- Connected User Dashboard: A secure, personal area for users to:
  - Manage their profile.
  - View their art investment portfolio.
  - Track their complete transaction history (purchases, sales, dividends).
  - Securely log out.
- Secondary Market: Sell your shares to other users on a dedicated marketplace.
- Secure Transactions: Simulated integration with a cryptocurrency wallet for transparent and secure transactions.
- Responsive Design: An elegant and accessible user interface on all devices, thanks to Tailwind CSS.

## 🛠️ Technologies Used

- Framework: Next.js
- Styling: Tailwind CSS
- Linting & Formatting: ESLint & Prettier

## 🚀 Getting Started

Follow these steps to get a development version of the project running on your local machine.

Prerequisites
Make sure you have Node.js (version 18.x or higher) and npm / yarn / pnpm installed.

node -v
npm -v

## Installation

Clone the repository:

```bash
git clone
cd weart
```

Install dependencies:

```bash
npm install
```

### Environment Setup

Create a `.env.local` file in the root directory with the following environment variables:

```env
NEXT_PUBLIC_API_BASE_URL=https://api.weart.exchange
NEXT_PUBLIC_BACKEND_BASE_URL=https://api-back-office.weart.exchange/
```

### Development

Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Production Deployment

For production deployment on a self-hosted server, follow these steps:

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Install PM2 globally (if not already installed):**
   ```bash
   npm install -g pm2
   ```

3. **Start the application with PM2:**
   ```bash
   pm2 start npm --name "weart" -- start
   ```

4. **Configure PM2 to restart on system reboot:**
   ```bash
   pm2 startup
   pm2 save
   ```

5. **Nginx Reverse Proxy Configuration:**
   
   Create or update your Nginx configuration file (e.g., `/etc/nginx/sites-available/weart`):

   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       # Redirect HTTP to HTTPS
       return 301 https://$server_name$request_uri;
   }

   server {
       listen 443 ssl http2;
       server_name your-domain.com;

       # SSL Configuration
       ssl_certificate /path/to/your/ssl/certificate.crt;
       ssl_certificate_key /path/to/your/ssl/private.key;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
       ssl_prefer_server_ciphers off;

       # Security Headers
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-XSS-Protection "1; mode=block" always;
       add_header X-Content-Type-Options "nosniff" always;
       add_header Referrer-Policy "no-referrer-when-downgrade" always;
       add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

       # Gzip Compression
       gzip on;
       gzip_vary on;
       gzip_min_length 1024;
       gzip_proxied expired no-cache no-store private auth;
       gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
           proxy_read_timeout 86400;
       }

       # Static Assets Optimization
       location /_next/static/ {
           proxy_pass http://localhost:3000;
           add_header Cache-Control "public, max-age=31536000, immutable";
       }

       location /static/ {
           proxy_pass http://localhost:3000;
           add_header Cache-Control "public, max-age=31536000, immutable";
       }
   }
   ```

6. **Enable the site and restart Nginx:**
   ```bash
   sudo ln -s /etc/nginx/sites-available/weart /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

7. **Monitor your application:**
   ```bash
   pm2 status
   pm2 logs weart
   pm2 monit
   ```

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.
