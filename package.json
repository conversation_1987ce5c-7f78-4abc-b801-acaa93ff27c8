{"name": "weart", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "libphonenumber-js": "^1.12.10", "motion": "^12.23.1", "negotiator": "^1.0.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-international-phone": "^4.6.0", "recharts": "^3.1.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/negotiator": "^0.6.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}