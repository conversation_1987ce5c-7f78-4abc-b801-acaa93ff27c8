import Link from 'next/link';
import React from 'react';
import { getDictionary } from '@/app/lib/get-translation';

export default async function DashboardLayout({ children }: { children: React.ReactNode }) {
  const dictionary = await getDictionary('fr');
  const nav = dictionary.dashboard.nav;

  return (
    <section>
      <nav className="bg-gray-100 p-4 mb-8">
        <ul className="flex space-x-4">
          <li>
            <Link href="/fr/tableau-de-bord/profil" className="text-blue-600 hover:underline">
              {nav.profile}
            </Link>
          </li>
          <li>
            <Link href="/fr/tableau-de-bord/portefeuille" className="text-blue-600 hover:underline">
              {nav.wallet}
            </Link>
          </li>
          <li>
            <Link href="/fr/tableau-de-bord/finance" className="text-blue-600 hover:underline">
              {nav.finance}
            </Link>
          </li>
          <li>
            <Link href="/fr/tableau-de-bord/historique" className="text-blue-600 hover:underline">
              {nav.history}
            </Link>
          </li>
        </ul>
      </nav>
 
      {children}
    </section>
  );
}
