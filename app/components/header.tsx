"use client";

import Image from "next/image";
import Link from "next/link";
import { Dictionary } from "../lib/get-translation";
import Ticker from "./Ticker";
import { useState, useEffect, useRef } from "react";
import { FiX } from "react-icons/fi";
import Button from "./pages/variants/button";
import { useRouter } from "next/navigation";
import { useAuth } from "../hooks/useAuth";
import { clearAuth, getRefreshToken } from "../lib/auth";
import { Kpi } from '../types/index';
import AccountDropdown from './pages/variants/AccountDropdown';

interface HeaderProps {
  kpis: Kpi[];
  dictionary: Dictionary;
  lang: "en" | "fr";
}

const Header = ({ kpis, dictionary, lang }: HeaderProps) => {
  const { isLoggedIn, isLoading } = useAuth();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState("$");
  const [selectedLanguage, setSelectedLanguage] = useState(lang);
  const [languageDisplay, setLanguageDisplay] = useState(lang === 'en' ? dictionary.nav.english : dictionary.nav.french);
  const [currencyDisplay, setCurrencyDisplay] = useState("$");
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const [isCurrencyOpen, setIsCurrencyOpen] = useState(false);
  const [isAccountDropdownOpen, setIsAccountDropdownOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownButtonRef = useRef<HTMLButtonElement>(null);
  const accountDropdownRef = useRef<HTMLDivElement>(null);

  const handleLogout = async () => {
    try {
      const refreshToken = getRefreshToken();
      if (refreshToken) {
        await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/logout`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            refresh_token: refreshToken,
            device_token: "web-app",
          }),
        });
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // Always clear local auth data regardless of API success/failure
      clearAuth();
      setIsAccountDropdownOpen(false);
      const loginPath = lang === "fr" ? "/fr/connexion" : "/login";
      router.push(loginPath);
    }
  };

  const handleAccountClick = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    if (isLoggedIn) {
      console.log('Person icon clicked, current state:', isAccountDropdownOpen);
      setIsAccountDropdownOpen(!isAccountDropdownOpen);
    } else {
      router.push(lang === "fr" ? "/fr/connexion" : "/login");
    }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // For the main dropdown, exclude both the modal and the button
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
        dropdownButtonRef.current && !dropdownButtonRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
      // For account dropdown, check if click is outside both dropdown and arrow button
      if (accountDropdownRef.current && !accountDropdownRef.current.contains(event.target as Node)) {
        const arrowButton = document.querySelector('.account-arrow-button');
        if (arrowButton && !arrowButton.contains(event.target as Node)) {
          setIsAccountDropdownOpen(false);
        }
      }
    };

    if (isDropdownOpen || isAccountDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen, isAccountDropdownOpen]);

  const navLinks = [
    { href: "/about", label: dictionary.nav.about },
    { href: "/artists", label: dictionary.nav.artists },
    { href: "/primary-market", label: dictionary.nav.primary_market },
    { href: "/secondary-market", label: dictionary.nav.secondary_market },
    { href: "/gallery", label: dictionary.nav.gallery },
  ];

  const getLocalizedPath = (path: string) => {
    if (lang === "fr") {
      const frSlugs: { [key: string]: string } = {
        "/about": "/a-propos",
        "/artists": "/artistes",
        "/primary-market": "/marche-primaire",
        "/secondary-market": "/marche-secondaire",
        "/gallery": "/galerie",
      };
      return `/fr${frSlugs[path] || path}`;
    }
    return path;
  };

  return (
    <header className="bg-white shadow-sm lg:shadow-none">
      <Ticker kpis={kpis} partLabel={dictionary.kpi.part} />

      {/* Mobile and Tablet Header */}
      <div className="xl:hidden">
        <nav className="container mx-auto px-4 py-3 flex justify-between items-center relative">
          {/* Left Icon */}
          <button onClick={() => setIsMenuOpen(true)} className="w-6 h-6 flex flex-col justify-center items-center flex-shrink-0 z-10">
            <Image src="/menu.svg" alt="WeArt Logo" width={40} height={40} className="w-auto h-5 sm:h-5" />
          </button>

          {/* Center Logo */}
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
            <Link href={lang === "fr" ? "/fr" : "/"}>
              <Image src="/logo.svg" alt="WeArt Logo" width={120} height={35} className="w-auto h-8 sm:h-9" />
            </Link>
          </div>

          {/* Right Icons */}
          <div className="flex items-center space-x-4 sm:space-x-6 text-xl sm:text-2xl flex-shrink-0 z-10">
            {/* Account Group */}
            <div className="relative flex items-center">
              <button onClick={(e) => handleAccountClick(e)}>
                <Image src="/person.svg" alt="person Logo" width={40} height={40} className="w-auto h-5 sm:h-5" />
              </button>

              {/* Dropdown for logged-in users */}
              {isLoggedIn && (
                <div className="relative">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('Arrow clicked, current state:', isAccountDropdownOpen);
                      setIsAccountDropdownOpen(!isAccountDropdownOpen);
                    }}
                    className="relative z-[51] ml-1 account-arrow-button"
                  >
                    <Image
                      src="/arrow-left.svg"
                      alt="dropdown arrow"
                      width={16}
                      height={16}
                      className={`ml-1 lg:ml-2 xl:ml-3 w-3.5 h-3.5 lg:w-3 lg:h-3 xl:w-4 xl:h-4 transition-transform duration-300 ease-in-out ${isMounted && isAccountDropdownOpen ? 'rotate-180' : 'rotate-0'}`}
                    />
                  </button>
                  {isAccountDropdownOpen && (
                    <AccountDropdown
                      dictionary={dictionary}
                      handleLogout={handleLogout}
                      accountDropdownRef={accountDropdownRef}
                    />
                  )}
                </div>
              )}
            </div>

            {/* Globe Icon */}
            <button onClick={() => setIsDropdownOpen(true)}>
              <Image src="/globe.svg" alt="globe Logo" width={40} height={40} className="w-auto h-5 sm:h-5" />
            </button>
          </div>
        </nav>
        <div className="px-4 pb-3">
          <Button variant="primary" className="w-full py-3 sm:py-4">
            {dictionary.user.buy}
          </Button>
        </div>
      </div>

      {isMenuOpen && (
        <div className="fixed inset-0 bg-gray-50 z-30 flex flex-col px-4 sm:px-6 py-6 sm:py-8 overflow-y-auto font-karla">
          <div className="relative flex justify-between items-center mb-8 sm:mb-10 md:mb-12 flex-shrink-0">
            {/* Close button */}
            <button
              onClick={() => setIsMenuOpen(false)}
              className="text-3xl sm:text-4xl text-black z-10 p-1 -ml-1"
            >
              <FiX strokeWidth={1} />
            </button>

            {/* Centered Logo */}
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
              <Link href={lang === "fr" ? "/fr" : "/"}>
                <Image
                  src="/logo.svg"
                  alt="WeArt Logo"
                  width={140}
                  height={40}
                  className="w-auto h-8 sm:h-9 md:h-10"
                />
              </Link>
            </div>

            {/* Spacer to balance the header */}
            <div className="w-8 h-8" />
          </div>

          {/* Navigation Links */}
          <nav className="flex flex-col font-karla font-sm">
            {navLinks.map((link) => (
              <div key={link.href} className="border-b border-gray-300">
                <Link
                  href={getLocalizedPath(link.href)}
                  className="block py-5 sm:py-5 uppercase tracking-wider text-sm sm:text-base ml-7"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {link.label}
                </Link>
              </div>
            ))}
          </nav>

          {/* Bottom Button under last link */}
          <div className="">
            <Button variant="primary" className="w-full py-3 sm:py-4 uppercase">
              {dictionary.user.buy}
            </Button>
          </div>
        </div>
      )}

      {/* Desktop Header */}
      <div className="hidden xl:flex w-full relative justify-center">
        <div className="w-full mx-4 lg:mx-[120px]">
          <nav className="grid grid-cols-4 lg:grid-cols-12 gap-4 lg:gap-5 items-center py-4 min-h-[60px] relative">
            {/* Logo - 2 columns */}
            <div className="col-span-2 flex items-center">
              <Link href={lang === "fr" ? "/fr" : "/"}>
                <Image src="/logo.svg" alt="WeArt Logo" width={170} height={49} className="w-auto h-8 lg:h-10 xl:h-12 max-w-[120px] lg:max-w-[150px] xl:max-w-[170px]" />
              </Link>
            </div>

            {/* Navigation Links - 6 columns, centered */}
            <div className="hidden lg:flex lg:col-span-6 items-center justify-center">
              <div className="flex items-center space-x-6 xl:space-x-8 uppercase text-sm lg:text-base xl:text-lg tracking-wide font-karla font-medium">
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={getLocalizedPath(link.href)}
                    className="text-black hover:text-black whitespace-nowrap text-xs lg:text-sm xl:text-base 2xl:text-lg"
                  >
                    {link.label}
                  </Link>
                ))}
              </div>
            </div>

            {/* Auth / Actions - 2 columns on mobile, 4 columns on desktop */}
            <div className="col-span-2 lg:col-span-4 flex items-center justify-end space-x-2 lg:space-x-3 xl:space-x-4 font-karla font-medium uppercase text-xs lg:text-sm xl:text-base 2xl:text-lg">
              {isLoading ? (
                <div className="w-16 lg:w-20 h-4 lg:h-6 bg-gray-200 animate-pulse rounded"></div>
              ) : isLoggedIn ? (
                <div className="relative">
                  <button
                    onClick={(e) => handleAccountClick(e)}
                    className="flex items-center text-black hover:text-black whitespace-nowrap"
                  >
                    <Image src="/person.svg" alt="user icon" width={20} height={20} className="w-3 h-3 lg:w-4 lg:h-4 xl:w-5 xl:h-5 2xl:w-7 2xl:h-5 mr-1 lg:mr-2" />
                    <span className="text-xs lg:text-sm xl:text-base 2xl:text-lg uppercase font-karla hidden lg:inline">{dictionary.user.account}</span>
                    {isLoggedIn && (
                      <Image
                        src="/arrow-left.svg"
                        alt="dropdown arrow"
                        width={16}
                        height={16}
                        className={`ml-1 lg:ml-2 xl:ml-3 w-2 h-2 lg:w-3 lg:h-3 xl:w-4 xl:h-4 transition-transform duration-300 ease-in-out ${isMounted && isAccountDropdownOpen ? 'rotate-180' : 'rotate-0'}`}
                      />
                    )}
                  </button>

                  {/* Account Dropdown Menu */}
                  {isAccountDropdownOpen && (
                    <AccountDropdown
                      dictionary={dictionary}
                      handleLogout={handleLogout}
                      accountDropdownRef={accountDropdownRef}
                    />
                  )}
                </div>
              ) : (
                /* Logged-out user: Show register | login */
                <>
                  <Link
                    href={lang === "fr" ? "/fr/inscription" : "/register"}
                    className="text-black hover:text-black whitespace-nowrap semibold text-xs lg:text-sm xl:text-base 2xl:text-lg"
                  >
                    {dictionary.user.register}
                  </Link>
                  <span className="text-[var(--color-primary)] text-xs lg:text-sm xl:text-base 2xl:text-lg -ml-2 mr-2">|</span>
                  <Link
                    href={lang === "fr" ? "/fr/connexion" : "/login"}
                    className="text-black hover:text-black whitespace-nowrap semibold text-xs lg:text-sm xl:text-base 2xl:text-lg"
                  >
                    {dictionary.user.login}
                  </Link>
                </>
              )}

              <div className="relative">
                <button
                  ref={dropdownButtonRef}
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsDropdownOpen(!isDropdownOpen);
                  }}
                  className="flex items-center text-black hover:text-black whitespace-nowrap"
                >
                  <span className="text-xs lg:text-sm xl:text-base 2xl:text-lg">{`${selectedLanguage.toUpperCase()} / ${selectedCurrency}`}</span>
                  <Image
                    src="/arrow-left.svg"
                    alt="dropdown arrow"
                    width={16}
                    height={16}
                    className={`ml-1 lg:ml-2 xl:ml-3 w-2 h-2 lg:w-3 lg:h-3 xl:w-4 xl:h-4 transition-transform duration-300 ease-in-out ${isMounted && isDropdownOpen ? 'rotate-180' : ''
                      }`}
                  />
                </button>
              </div>

              <div className="ml-1 lg:ml-2 xl:ml-3 flex-shrink-0">
                <Button>
                  {dictionary.user.buy}
                </Button>
              </div>
            </div>
          </nav>
          
          {/* Border that spans full header width */}
          <div className="border-b border-gray-200"></div>
        </div>
      </div>

      {isDropdownOpen && (
        <div className="fixed inset-0 z-40 bg-black/85 flex items-center justify-center px-4 py-4 sm:px-0 sm:py-0">
          <div ref={dropdownRef} className="relative bg-white p-4 sm:p-8 md:p-12 w-full max-w-md sm:max-w-md md:max-w-3xl min-h-[450px] sm:min-h-[450px] shadow-2xl border border-gray-200 ">
            <button
              onClick={() => setIsDropdownOpen(false)}
              className="absolute pt-2 top-3 right-3 sm:top-4 sm:right-4 text-2xl sm:text-3xl text-black"
            >
              <FiX strokeWidth={1} />
            </button>

            <h2 className={`text-2xl sm:text-3xl md:text-4xl mb-10 sm:mb-12 md:mb-16 text-center font-prata pt-6`}>{dictionary.modal.title}</h2>

            <div className="space-y-8 sm:space-y-8 md:space-y-10 max-w-2xs mx-auto md:max-w-[600px] md:mx-auto">
              {/* Language Selector */}
              <div className="relative">
                <button onClick={() => { setIsLanguageOpen(!isLanguageOpen); setIsCurrencyOpen(false); }} className="w-full flex justify-between items-center py-4 sm:py-3 px-4 border-b-2 border-gray-200">
                  <span className="text-base sm:text-lg">{languageDisplay}</span>
                  <svg className={`w-4 h-4 sm:w-5 sm:h-5 transition-transform ${isLanguageOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {isLanguageOpen && (
                  <div className="absolute top-full left-0 w-full bg-white shadow-lg z-10 ">
                    <div className="divide-y divide-gray-200">
                      <button onClick={() => { setLanguageDisplay(dictionary.nav.language); setIsLanguageOpen(false); }} className={`w-full text-left py-3 px-4 text-sm sm:text-base ${languageDisplay === dictionary.nav.language ? 'bg-[#B9924F] text-white' : 'hover:bg-gray-100'}`}>{dictionary.nav.language}</button>
                      <button
                        onClick={() => {
                          setSelectedLanguage("en");
                          setLanguageDisplay(dictionary.nav.english);
                          setIsLanguageOpen(false);
                        }}
                        className={`w-full text-left py-3 px-4 text-sm sm:text-base ${languageDisplay === dictionary.nav.english ? "bg-[var(--color-primary)] text-white" : "hover:bg-gray-100"}`}>
                        {dictionary.nav.english}
                      </button>
                      <button
                        onClick={() => {
                          setSelectedLanguage("fr");
                          setLanguageDisplay(dictionary.nav.french);
                          setIsLanguageOpen(false);
                        }}
                        className={`w-full text-left py-3 px-4 text-sm sm:text-base ${languageDisplay === dictionary.nav.french ? "bg-[var(--color-primary)] text-white" : "hover:bg-gray-100"}`}>
                        {dictionary.nav.french}
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Currency Selector */}
              <div className="relative">
                <button onClick={() => { setIsCurrencyOpen(!isCurrencyOpen); setIsLanguageOpen(false); }} className="w-full flex justify-between items-center py-3 px-4 border-b-2 border-gray-200">
                  <span className="text-base sm:text-lg">{currencyDisplay}</span>
                  <svg className={`w-4 h-4 sm:w-5 sm:h-5 transition-transform ${isCurrencyOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {isCurrencyOpen && (
                  <div className="absolute top-full left-0 w-full bg-white shadow-lg z-10">
                    <div className="divide-y divide-gray-200">
                      <button onClick={() => { setCurrencyDisplay(dictionary.nav.currency); setIsCurrencyOpen(false); }} className={`w-full text-left py-3 px-4 text-sm sm:text-base ${currencyDisplay === dictionary.nav.currency ? 'bg-[var(--color-primary)] text-white' : 'hover:bg-gray-100'}`}>{dictionary.nav.currency}</button>
                      <button
                        onClick={() => { setSelectedCurrency("$"); setCurrencyDisplay("$"); setIsCurrencyOpen(false); }}
                        className={`w-full text-left py-3 px-4 text-sm sm:text-base ${currencyDisplay === "$" ? "bg-[var(--color-primary)] text-white" : "hover:bg-gray-100"}`}>
                        $
                      </button>
                      <button
                        onClick={() => { setSelectedCurrency("€"); setCurrencyDisplay("€"); setIsCurrencyOpen(false); }}
                        className={`w-full text-left py-3 px-4 text-sm sm:text-base ${currencyDisplay === "€" ? "bg-[var(--color-primary)]  text-white" : "hover:bg-gray-100"}`}>
                        €
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-12 sm:mt-12 flex justify-center mb-3 ">
              <Button
                onClick={() => {
                  // Navigate based on selected language
                  if (selectedLanguage === "fr" && lang === "en") {
                    router.push("/fr/inscription");
                  } else if (selectedLanguage === "en" && lang === "fr") {
                    router.push("/register");
                  }
                  setIsDropdownOpen(false);
                }}
                variant="auth"
                width="w-[290px] md:w-80 lg:w-96"
                className="font-karla uppercase py-4 sm:py-3"
              >
                {dictionary.modal.save_preferences}
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
