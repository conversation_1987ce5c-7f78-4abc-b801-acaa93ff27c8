"use client";

import Image from "next/image";
import { useState, useEffect, useCallback } from "react";


interface GalleryItem {
  _id: string;
  title: {
    en: string;
    fr: string;
  };
  img: string;
}

interface PhotoGalleryProps {
  galleries: GalleryItem[];
  baseUrl: string;
  lang: "en" | "fr";
}

export default function PhotoGallery({
  galleries,
  baseUrl,
  lang,
}: PhotoGalleryProps) {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

  const openFullscreen = (index: number) => {
    setSelectedIndex(index);
  };

  const closeFullscreen = () => {
    setSelectedIndex(null);
  };

  const goToPrevious = useCallback(() => {
    if (selectedIndex !== null && selectedIndex > 0) {
      setSelectedIndex(selectedIndex - 1);
    }
  }, [selectedIndex]);

  const goToNext = useCallback(() => {
    if (selectedIndex !== null && selectedIndex < galleries.length - 1) {
      setSelectedIndex(selectedIndex + 1);
    }
  }, [selectedIndex, galleries.length]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (selectedIndex === null) return;

      switch (event.key) {
        case "Escape":
          closeFullscreen();
          break;
        case "ArrowLeft":
          goToPrevious();
          break;
        case "ArrowRight":
          goToNext();
          break;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedIndex, goToPrevious, goToNext]);

  useEffect(() => {
    if (selectedIndex !== null) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [selectedIndex]);

  if (!galleries || galleries.length === 0) {
    return (
      <div className="flex items-center justify-center py-20">
        <p className="text-gray-500">No images available</p>
      </div>
    );
  }

  return (
    <>
      {/* Gallery Grid */}
      <div className="container mx-auto px-4 lg:px-6 xl:px-8 2xl:px-12">
        <div className="w-full" style={{ paddingLeft: '0px', paddingRight: '0px', marginLeft: 'calc(auto + 65px)', marginRight: 'calc(auto - 65px)', maxWidth: '1500px' }}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-9 pb-5">
            {galleries.map((item, index) => (
              <div
                key={item._id}
                className="group relative aspect-[3/4] overflow-hidden cursor-pointer bg-white border border-gray-200 shadow-lg"
                onClick={() => openFullscreen(index)}
              >
                <Image
                  src={`${baseUrl}${item.img}`}
                  alt={item.title[lang]}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Image Titles Below */}
      <div className="container mx-auto px-4 lg:px-6 xl:px-8 2xl:px-12">
        <div className="w-full" style={{ paddingLeft: '0px', paddingRight: '0px', marginLeft: 'calc(auto + 65px)', marginRight: 'calc(auto - 65px)', maxWidth: '1500px' }}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-9 pb-5">
            {galleries.map((item) => (
              <div key={`title-${item._id}`} className="text-center">
                <p className="text-xl font-karla text-black font-medium">
                  {item.title[lang]}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Fullscreen Modal */}
      {selectedIndex !== null && (
        <div className="fixed inset-0 z-50 flex items-center justify-center" style={{ backgroundColor: 'rgba(0, 0, 0, 0.80)' }}>
          {/* Close Button */}
          <button
            onClick={closeFullscreen}
            className="absolute top-8 right-60 z-60 text-white hover:text-gray-300 transition-colors p-2"
            aria-label="Close fullscreen"
          >
            <svg width={32} height={32} fill="none" stroke="currentColor" viewBox="0 0 20 20">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Navigation Buttons */}
          {selectedIndex > 0 && (
            <button
              onClick={goToPrevious}
              className="absolute left-60 top-1/2 -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors p-2"
              aria-label="Previous image"
            >
              <Image src="/previous-img.svg" alt="Previous" width={32} height={32} className="w-8 h-8 brightness-0 invert" />
            </button>
          )}

          {selectedIndex < galleries.length - 1 && (
            <button
              onClick={goToNext}
              className="absolute right-60 top-1/2 -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors p-2"
              aria-label="Next image"
            >
              <Image src="/Next-img.svg" alt="Next" width={32} height={32} className="w-8 h-8 brightness-0 invert" />
            </button>
          )}

          {/* Main Content Container */}
          <div className="relative w-full h-full flex flex-col items-center justify-center px-20 py-16">
            
            {/* Main Image Container with Title Overlay */}
            <div className="relative flex items-center justify-center mb-6">
              <div className="relative w-[415px] h-[520px] animate-in slide-in-from-right duration-500">
                <Image
                  src={`${baseUrl}${galleries[selectedIndex].img}`}
                  alt={galleries[selectedIndex].title[lang]}
                  fill
                  className="object-cover drop-shadow-2xl"
                  sizes="300px"
                  priority
                />
                {/* Title Overlay on Image */}
                <div className="absolute bottom-1 left-1/2 -translate-x-1/2 z-10 text-center">
                  <h2 className="text-white font-karla text-xl font-medium px-4 py-2 whitespace-nowrap">
                    {galleries[selectedIndex].title[lang]}
                  </h2>
                </div>
              </div>
            </div>

            {/* Image Information */}
            <div className="text-center w-full max-w-6xl">
              {/* Thumbnail Strip */}
              <div className="flex items-center justify-center space-x-[14px] overflow-x-auto pb-3">
                {galleries.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedIndex(index)}
                    className={`relative flex-shrink-0 w-28 h-28 overflow-hidden ${
                      index === selectedIndex 
                        ? 'border-1 border-white ' 
                        : ''
                    }`}
                  >
                    <Image
                      src={`${baseUrl}${item.img}`}
                      alt={item.title[lang]}
                      fill
                      className="object-cover scale-120"
                      sizes="80px"
                    />
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Background Click to Close */}
          <div 
            className="absolute inset-0 cursor-pointer -z-10" 
            onClick={closeFullscreen}
            aria-label="Click to close"
          />
        </div>
)}
    </>
  );
}
