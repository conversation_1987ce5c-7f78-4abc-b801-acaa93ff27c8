"use client";

import { useNavigationLoading } from "../hooks/useNavigationLoading";

export default function LoadingAnimation() {
  const isLoading = useNavigationLoading();

  if (!isLoading) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-1 bg-gradient-to-r from-[#b9924f] via-[#d5b77c] to-[#b9924f]">
      <div className="h-full w-full animate-loading-bar"></div>
    </div>
  );
}
