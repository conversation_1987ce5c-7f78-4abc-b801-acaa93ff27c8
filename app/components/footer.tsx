import Image from "next/image";
import Link from "next/link";

import { getDictionary } from "../lib/get-translation";

const Footer = async ({ lang = "fr" }: { lang?: "en" | "fr" }) => {
  const dictionary = await getDictionary(lang);
  const { footer } = dictionary;

  const weartLinks = {
    home: lang === 'fr' ? '/fr' : '/',
    about: lang === 'fr' ? '/fr/a-propos' : '/about',
    artists: lang === 'fr' ? '/fr/artistes' : '/artists',
    primary: lang === 'fr' ? '/fr/marche-primaire' : '/primary-market',
    secondary: lang === 'fr' ? '/fr/marche-secondaire' : '/secondary-market',
    gallery: lang === 'fr' ? '/fr/galerie' : '/gallery',
  };

  return (
    <footer className="bg-black text-white sm:pt-10  pt-8 pb-8 px-4 sm:px-6 lg:px-8 font-karla">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-center mb-10">
          <Image
            src="/logo-powered-by-weex.svg"
            alt="WEART Powered by WEEX"
            width={160}
            height={40}
          />
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 sm:gap-6 lg:gap-12">
          {/* Logo Section - Hidden on mobile */}
          <div className="hidden lg:flex lg:col-span-1 justify-start items-start pt-1">
            <Image
              src="/logo-footer.svg"
              alt="WEART"
              width={125}
              height={130}
            />
          </div>

          {/* WEART Section */}
          <div className="">
            <h3 className="font-bold text-white mb-3 sm:mb-3 text-base sm:text-lg uppercase tracking-wider">{footer.weart.title}</h3>
            <ul className="space-y-1 sm:space-y-1 text-sm sm:text-sm">
              <li>
                <Link href={weartLinks.home} className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weart.home}
                </Link>
              </li>
              <li>
                <Link href={weartLinks.about} className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weart.about}
                </Link>
              </li>
              <li>
                <Link href={weartLinks.artists} className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weart.artists}
                </Link>
              </li>
              <li>
                <Link href={weartLinks.primary} className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weart.primary}
                </Link>
              </li>
              <li>
                <Link href={weartLinks.secondary} className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weart.secondary}
                </Link>
              </li>
              <li>
                <Link href={weartLinks.gallery} className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weart.gallery}
                </Link>
              </li>
            </ul>
          </div>

          {/* DIVERS Section */}
          <div className="">
            <h3 className="font-bold text-white mb-3 sm:mb-3 text-base sm:text-lg uppercase tracking-wider">{footer.divers.title}</h3>
            <ul className="space-y-1 sm:space-y-1 text-sm sm:text-sm">
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.divers.team}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.divers.press}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.divers.faq}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.divers.blog}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.divers.opening}
                </Link>
              </li>
            </ul>
          </div>

          {/* WEEX Section */}
          <div className="">
            <h3 className="font-bold text-white mb-3 sm:mb-3 text-base sm:text-lg uppercase tracking-wider">{footer.weex.title}</h3>
            <ul className="space-y-1 sm:space-y-1 text-sm sm:text-sm">
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weex.board}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weex.structure}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weex.depot}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weex.security}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.weex.coverage}
                </Link>
              </li>
            </ul>
          </div>

          {/* CONTACT Section */}
          <div className="">
            <h3 className="font-bold text-white mb-3 sm:mb-3 text-base sm:text-lg uppercase tracking-wider">{footer.contact.title}</h3>
            <ul className="space-y-1 sm:space-y-1 text-sm sm:text-sm">
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.contact.write}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.contact.meet}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.contact.apply}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.contact.collaborate}
                </Link>
              </li>
            </ul>
          </div>

          {/* LEGAL Section */}
          <div className="sm:block">
            <h3 className="font-bold text-white mb-3 text-lg uppercase tracking-wider">{footer.legal.title}</h3>
            <ul className="space-y-1 text-sm">
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.legal.terms}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.legal.conditions}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.legal.privacy}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.legal.disclosure}
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-200 hover:text-white transition-colors duration-200">
                  {footer.legal.cookies}
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom section */}
        <div className="block border-t border-white mt-9 sm:py-6 py-4">
          <div className="flex flex-col md:flex-row justify-between items-center text-xs text-gray-400">
            <div className="flex items-center -ml-5 space-x-1 mb-4 md:mb-0 md:flex-1">
              <a
                href="https://www.instagram.com/weart.exchange/"
                aria-label="Instagram"
                className=""
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  src="/instagram.svg"
                  alt="Instagram"
                  width={29}
                  height={29}
                  className="h-16 w-16"
                />
              </a>
              <a
                href="https://www.facebook.com/profile.php?id=61557217192849"
                aria-label="Facebook"
                className=""
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  src="/facebook.svg"
                  alt="Facebook"
                  width={29}
                  height={29}
                  className="h-7 w-7"
                />
              </a>
            </div>

            <div className="text-center text-gray-100 text-sm font-light mb-4 md:mb-0">
              <p className="flex items-center justify-center gap-1">
                <span className="text-sm">©</span>
                {footer.copyright}
              </p>
              <p>{footer.license}</p>
            </div>

            <div className="flex items-center space-x-4 md:flex-1 justify-end">
              <Image
                src="/logo-blockchain.svg"
                alt="Blockchain"
                width={110}
                height={26}
              />
              <Image
                src="/logo-kaleido.png"
                alt="Kaleido"
                width={82}
                height={26}
              />
              <Image
                src="/logo-lab-ft.svg"
                alt="Lab FT"
                width={100}
                height={24}
              />
            </div>
          </div>
        </div>


      </div>
    </footer>
  );
};

export default Footer;
