"use client";

import { motion } from "framer-motion";

interface Kpi {
  _id: string;
  artist_name: string;
  value_usd: string;
  percentage: string;
  direction: "up" | "down";
}

interface TickerProps {
  kpis: Kpi[];
  partLabel: string;
}

const Ticker = ({ kpis, partLabel }: TickerProps) => {
  const duplicatedKpis = [...kpis, ...kpis];

  return (
    <div className="bg-black text-white overflow-hidden whitespace-nowrap relative h-10 flex items-center">
      <motion.div
        className="flex"
        animate={{
          x: ["0%", "-100%"],
          transition: {
            ease: "linear",
            duration: 110,
            repeat: Infinity,
          },
        }}
      >
        {duplicatedKpis.map((kpi, index) => (
          <div key={`${kpi._id}-${index}`} className="flex items-center mx-10">
            <span>{kpi.artist_name}</span>
            <span className="mx-2">
              {kpi.value_usd}$ / {partLabel}
            </span>
            <span
              className={`flex items-center ${
                kpi.direction === "up" ? "text-green-500" : "text-red-500"
              }`}
            >
              {kpi.percentage}%
              <svg
                className="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 10l7-7m0 0l7 7m-7-7v18"
                ></path>
              </svg>
            </span>
          </div>
        ))}
      </motion.div>
    </div>
  );
};

export default Ticker;
