"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Dictionary } from "../../lib/get-translation";
import { useAuth } from "../../hooks/useAuth";
import { clearAuth } from "../../lib/auth";

interface DashboardPageProps {
  dictionary: Dictionary;
  lang: string;
}

export default function DashboardPage({ dictionary, lang }: DashboardPageProps) {
  const { isLoggedIn, user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isLoggedIn) {
      const loginPath = lang === "fr" ? "/fr/connexion" : "/login";
      router.push(loginPath);
    }
  }, [isLoggedIn, isLoading, router, lang]);

  const handleLogout = () => {
    clearAuth();
    const loginPath = lang === "fr" ? "/fr/connexion" : "/login";
    router.push(loginPath);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">
          {dictionary.auth.dashboard.loading}
        </div>
      </div>
    );
  }

  if (!isLoggedIn) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {dictionary.auth.dashboard.title}
              </h1>
              <p className="text-gray-600 mt-2">
                {dictionary.auth.dashboard.welcome}{user?.name ? `, ${user.name as string}` : ""}
              </p>
            </div>
            <button
              onClick={handleLogout}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              {dictionary.auth.dashboard.logout}
            </button>
          </div>
        </div>

        {/* User Info */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {dictionary.auth.dashboard.account_info}
          </h2>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                {dictionary.auth.login.email}
              </label>
              <p className="mt-1 text-sm text-gray-900">{user?.email as string}</p>
            </div>
            {user?.name ? (
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Name
                </label>
                <p className="mt-1 text-sm text-gray-900">{user.name as string}</p>
              </div>
            ) : null}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {dictionary.auth.dashboard.quick_actions}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <button
              onClick={() => { }}
              className="p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg text-left transition-colors"
            >
              <h3 className="font-medium text-indigo-900">
                {dictionary.auth.dashboard.view_artists}
              </h3>
              <p className="text-sm text-indigo-700 mt-1">
                {dictionary.auth.dashboard.view_artists_desc}
              </p>
            </button>

            <button
              onClick={() => { }}
              className="p-4 bg-green-50 hover:bg-green-100 rounded-lg text-left transition-colors"
            >
              <h3 className="font-medium text-green-900">
                {dictionary.auth.dashboard.gallery}
              </h3>
              <p className="text-sm text-green-700 mt-1">
                {dictionary.auth.dashboard.gallery_desc}
              </p>
            </button>

            <button
              onClick={() => { }}
              className="p-4 bg-purple-50 hover:bg-purple-100 rounded-lg text-left transition-colors"
            >
              <h3 className="font-medium text-purple-900">
                {dictionary.auth.dashboard.market}
              </h3>
              <p className="text-sm text-purple-700 mt-1">
                {dictionary.auth.dashboard.market_desc}
              </p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
