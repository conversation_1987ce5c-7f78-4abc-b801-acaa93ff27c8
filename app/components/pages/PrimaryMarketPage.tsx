import { getDictionary } from "../../lib/get-translation";
import { ApiResponse } from "../../types/market";
import PrimaryMarketClient from "./PrimaryMarketClient";

async function getPrimaryArtworkData(): Promise<ApiResponse> {
  try {
    const apiUrl = "https://api.weart.exchange/paintings/?type=primary&limit=1&offset=0&currency=USD";
    console.log('Fetching from URL:', apiUrl);

    const response = await fetch(apiUrl, {
      next: { revalidate: 3600 }
      // Removed cache: 'no-store' to allow static generation
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);
      throw new Error(`Failed to fetch primary artwork data: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API Response data:', data);
    return data;
  } catch (error) {
    console.error('Error fetching primary artwork data:', error);
    // Return fallback data instead of throwing
    return {
      paintings: [],
      base_url: "",
      count: 0,
      next: null,
      previous: null
    };
  }
}

export default async function PrimaryMarketPage({ lang }: { lang: "en" | "fr" }) {
  const dictionary = await getDictionary(lang);
  const data = await getPrimaryArtworkData();

  // Add null check for painting data
  const painting = data.paintings?.[0];

  if (!painting) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {lang === "fr" ? "Marché Primaire" : "Primary Market"}
            </h1>
            <p className="text-gray-600">
              {lang === "fr" ? "Aucune œuvre disponible pour le moment." : "No artworks available at the moment."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PrimaryMarketClient
      dictionary={dictionary}
      artworkData={data}
      lang={lang}
    />
  );
}
