import { getDictionary } from "../../lib/get-translation";
import PhotoGallery from "../PhotoGallery";

interface GalleryItem {
  _id: string;
  title: {
    en: string;
    fr: string;
  };
  img: string;
}

interface ApiResponse {
  galleries: GalleryItem[];
  base_url: string;
}

async function getGalleryData(): Promise<ApiResponse> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/content/galleries`,
      { next: { revalidate: 3600 } }
    );
    if (!response.ok) {
      throw new Error('Failed to fetch gallery data');
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching gallery data:", error);
    return { galleries: [], base_url: "" };
  }
}
export default async function GalleryPage({ lang }: { lang: "en" | "fr" }) {
  const [dictionary, galleryData] = await Promise.all([
    getDictionary(lang),
    getGalleryData(),
  ]);

  return (
    <div className="min-h-screen bg-white py-14">
      <div className="container mx-auto py-20">
        <div className="text-center">
          <h1 className="text-5xl font-prata text-black mb-3 font-medium">
            {dictionary.nav.gallery.charAt(0).toUpperCase() + dictionary.nav.gallery.slice(1).toLowerCase()}
          </h1>
        </div>
      </div>

      {/* Full-width gallery */}
      <PhotoGallery
        galleries={galleryData.galleries}
        baseUrl={galleryData.base_url}
        lang={lang}
      />
    </div>
  );
}
