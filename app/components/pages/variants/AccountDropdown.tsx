import React from 'react';
import { Dictionary } from '../../../lib/get-translation';

interface AccountDropdownProps {
  dictionary: Dictionary;
  handleLogout: () => void;
  accountDropdownRef: React.Ref<HTMLDivElement>;
}

const AccountDropdown: React.FC<AccountDropdownProps> = ({ dictionary, handleLogout, accountDropdownRef }) => {
  return (
    <div
      ref={accountDropdownRef}
      className="absolute top-full -right-10 mt-4 w-44 bg-white shadow-lg border border-gray-200  z-50"
    >
      <div className="">
        <button className="w-full text-left px-4 py-2 text-base hover:bg-gray-100 text-black font-karla capitalize">
          {dictionary.user.account_menu.profile}
        </button>
        <button className="w-full text-left px-4 py-2 text-base hover:bg-gray-100 text-black font-karla capitalize">
          {dictionary.user.account_menu.wallet}
        </button>
        <button className="w-full text-left px-4 py-2 text-base hover:bg-gray-100 text-black font-karla capitalize">
          {dictionary.user.account_menu.finance}
        </button>
        <button className="w-full text-left px-4 py-2 text-base hover:bg-gray-100 text-black font-karla capitalize">
          {dictionary.user.account_menu.history}
        </button>
        <hr className=" border-gray-300" />
        <button
          onClick={handleLogout}
          className="w-full text-left px-4 py-2 text-base hover:bg-gray-100  text-black font-karla capitalize"
        >
          {dictionary.user.account_menu.logout}
        </button>
      </div>
    </div>
  );
};

export default AccountDropdown;
