import React from "react";
import { clsx } from "clsx";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: "primary" | "auth" | "details";
  width?: string;
  className?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ children, variant = "primary", width, className, ...props }, ref) => {
    const baseClasses = "font-medium transition-colors focus:outline-none disabled:opacity-50 font-karla";

    const variants = {
      primary: "flex items-center justify-center px-6 xl:px-8 py-2 xl:py-3 bg-[var(--color-primary)] hover:bg-[#a7813d] text-white text-base xl:text-lg whitespace-nowrap uppercase min-w-[120px] xl:min-w-[140px]",
      auth: "py-3 sm:py-4 bg-black hover:bg-[var(--color-primary)] text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300 ease-in-out text-base sm:text-lg",
      details: "py-3 border font-bold border-black font-karla text-md text-black hover:bg-black hover:text-white bg-transparent transition-colors duration-300 ease-in-out"
    };

    return (
      <button
        ref={ref}
        className={clsx(
          baseClasses,
          variants[variant],
          width,
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
