import Image from "next/image";
import { getDictionary } from "../../lib/get-translation";
import Button from "./variants/button";

async function fetchPainting() {
  const res = await fetch(
    "https://api.weart.exchange/paintings/?type=primary&limit=1&offset=0&currency=USD",
    { cache: "no-store" }
  );
  if (!res.ok) throw new Error("Failed to fetch painting data");
  return res.json();
}

export default async function AboutPage({ lang }: { lang: "en" | "fr" }) {
  const dictionary = await getDictionary(lang);
  const paintingData = await fetchPainting();
  // paintingData.paintings[0] is the main painting object
  return (
    <section className="relative min-h-[82vh] w-full overflow-hidden m-0 p-0 overflow-y-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full z-0">
        <Image
          src="/images/Museum.png"
          alt="Museum background"
          fill
          style={{ objectFit: "cover" }}
          priority
          className="transform transition-transform duration-300"
        />
        <div className="absolute top-0 left-0 w-full h-full " />
      </div>
      {/* Content */}
      <div className="absolute top-0 left-0 w-full z-10 flex flex-col items-center px-0 pt-15 m-0">
        <h1 className="text-white text-3xl sm:text-4xl lg:text-5xl font-prata text-center mb-12">
          {dictionary.about.mission}
        </h1>
        {/* Painting Card Overlay */}
        {paintingData.paintings && paintingData.paintings[0] && (
          <div className="relative flex flex-col items-center justify-center w-[450px] max-w-lg mx-auto">
            {/* Card border overlay */}
            <div className="absolute inset-0 border-2 border-white/80  pointer-events-none" style={{ zIndex: 1 }} />
            <div className="relative flex flex-col items-center w-full bg-transparent shadow-xl overflow-hidden">
              {/* Painting Image */}
              <div className="w-full flex justify-center pt-10 pb-2 bg-transparent">
                <div className="w-[240px] h-[300px] flex items-center justify-center bg-transparent">
                  <Image
                    src={`${paintingData.base_url}${paintingData.paintings[0].cover_img}`}
                    alt={paintingData.paintings[0].title[lang]}
                    width={240}
                    height={300}
                    className="object-contain transform scale-90 transition-transform duration-300"
                  />
                </div>
              </div>
              {/* Price and Parts */}
              <div className="flex w-[90%] justify-between px-8 py-2 bg-white/30 text-white text-center text-base font-karla -mt-2 mb-4">
                <div className="flex-1 text-left -ml-2">
                  <div className="text-md mb-1">{dictionary.about.initial_price}</div>
                  <div className="text-2xl">{paintingData.paintings[0].purchase_price_per_part.value} $</div>
                </div>
                <div className="flex-1 text-right -mr-2">
                  <div className="text-md mb-1">{dictionary.about.parts_remaining}</div>
                  <div className="text-2xl">{paintingData.paintings[0].total_part - paintingData.paintings[0].sold_out_part} / {paintingData.paintings[0].total_part}</div>
                </div>
              </div>
              {/* Title, Artist, Button */}
              <div className="flex flex-col sm:flex-row items-center justify-between w-full px-8 py-5 bg-white mt-2">
                <div className="flex-1 text-left mt-2">
                  <div className="text-gray-700 text-base font-karla ">{paintingData.paintings[0].title[lang]}</div>
                  <div className="text-black text-xl font-bold font-karla tracking-wide">Pablo PICASSO</div>
                </div>
                <div className="flex-1 flex justify-end mt-4 sm:mt-0">
                  <Button variant="primary" className="w-40">
                    {dictionary.about.buy_button}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
