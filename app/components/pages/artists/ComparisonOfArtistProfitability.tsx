"use client";

import { Artist } from "../../../types/artist";
import React, { useState, useEffect } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { FiInfo } from "react-icons/fi";

interface ComparisonOfArtistProfitabilityProps {
  artists: Artist[];
  dictionary: {
    title: string;
    y_axis_label: string;
    description?: string;
  };
}

const ComparisonOfArtistProfitability: React.FC<
  ComparisonOfArtistProfitabilityProps
> = ({ artists, dictionary }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768); // Using 768px as mobile breakpoint
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Predefined colors for specific artists to match the design
  const artistColors: Record<string, string> = {
    "Jean-Michel Basquiat": "var(--artist-basquiat)",
    "Pierre Soulages": "var(--artist-soulages)",
    "Andy Warhol": "var(--artist-warhol)",
    "Pablo PICASSO": "var(--artist-picasso)",
    "Zao Wou-ki": "var(--artist-zao)",
    "David Hockney": "var(--artist-hockney)",
  };

  // Fallback colors for any additional artists
  const fallbackColors = [
    "var(--fallback-1)",
    "var(--fallback-2)",
    "var(--fallback-3)",
    "var(--fallback-4)",
    "var(--fallback-5)",
    "var(--fallback-6)",
    "var(--fallback-7)",
  ];

  type ChartData = {
    year: string;
    [key: string]: number | string;
  };

  const data = artists.reduce((acc: ChartData[], artist) => {
    artist.evolution_list.forEach((evolution) => {
      let yearData = acc.find((d) => d.year === evolution._id);
      if (!yearData) {
        yearData = { year: evolution._id };
        acc.push(yearData);
      }
      yearData[artist.name] = evolution.totalValue || 0;
    });
    return acc;
  }, []);

  data.sort((a, b) => parseInt(a.year) - parseInt(b.year));

  const getColorForArtist = (artistName: string, index: number) => {
    return (
      artistColors[artistName] || fallbackColors[index % fallbackColors.length]
    );
  };

  const description = dictionary.description || "";

  return (
    <div className={`w-full bg-white  ${isMobile ? 'px-5 py-6 mt-7 -ml-2' : 'px-6 py-8 lg:px-8 lg:py-12'}`}>
      {/* Title Section */}
      <div className={`text-center ${isMobile ? 'mb-4' : 'mb-6'}`}>
        <h1 className={`font-prata text-black ${isMobile ? 'text-3xl mb-2' : 'mb-3'}`} style={isMobile ? {} : { fontSize: '45px' }}>
          {dictionary.title}
          <FiInfo className={`inline-block ml-2 text-black ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} style={{ verticalAlign: 'super' }} />
        </h1>
        <p className={`text-black mx-auto leading-relaxed font-karla ${isMobile ? 'max-w-full text-sm px-1' : 'max-w-4xl'}`} style={isMobile ? {} : { fontSize: '20px' }}>
          {description}
        </p>
      </div>

      {/* Chart Section */}
      <div className={`${isMobile ? 'h-[280px] mb-4' : 'h-[250px] md:h-[350px] lg:h-[450px] mb-6 -mx-16 lg:-ml-32'}`}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={isMobile ? { top: 15, right: 0, left: 0, bottom: 45 } : { top: 20, right: 20, left: 40, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="year"
              tick={{ fontSize: isMobile ? 8 : 10, fill: "#666" }}
              axisLine={{ stroke: "#e0e0e0" }}
              tickLine={{ stroke: "#e0e0e0" }}
              interval={isMobile ? 1 : "preserveStartEnd"}
              angle={0}
              textAnchor="middle"
              height={isMobile ? 40 : 40}
            />
            <YAxis
              tick={{ fontSize: isMobile ? 8 : 10, fill: "#666" }}
              axisLine={false}
              tickLine={false}
              domain={[0, 2000]}
              ticks={[0, 500, 1000, 1500, 2000]}
              width={isMobile ? 35 : 60}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "white",
                border: "1px solid #e0e0e0",
                borderRadius: "6px",
                boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                fontSize: isMobile ? "10px" : "12px",
                padding: isMobile ? "6px" : "12px",
              }}
            />
            {artists.map((artist, index) => (
              <Line
                key={artist._id}
                type="monotone"
                dataKey={artist.name}
                stroke={getColorForArtist(artist.name, index)}
                strokeWidth={isMobile ? 2 : 2.5}
                dot={false}
                activeDot={{ r: isMobile ? 3 : 4, strokeWidth: 0 }}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>

        {/* Legend Section */}
        <div className={`font-karla  ${isMobile ? '-mt-14 px-2' : 'flex flex-wrap justify-center ml-15 gap-9 -mt-15'}`}>
          {isMobile ? (
            <div className="flex flex-col gap-y-1" style={{ marginLeft: '25px', marginRight: '0px' }}>
              {/* First row - 3 artists */}
              <div className="flex justify-center gap-x-2">
                {artists.slice(0, 3).map((artist, index) => (
                  <div key={artist._id} className="flex items-center gap-1 flex-shrink-0">
                    <div
                      className="rounded-full flex-shrink-0 w-2 h-2"
                      style={{ backgroundColor: getColorForArtist(artist.name, index) }}
                    />
                    <span
                      className="font-medium text-[11px] leading-tight whitespace-nowrap"
                      style={{ color: getColorForArtist(artist.name, index) }}
                      title={artist.name}
                    >
                      {artist.name}
                    </span>
                  </div>
                ))}
              </div>
              {/* Second row - remaining artists */}
              <div className="flex justify-center gap-x-2">
                {artists.slice(3).map((artist, index) => (
                  <div key={artist._id} className="flex items-center gap-1 flex-shrink-0">
                    <div
                      className="rounded-full flex-shrink-0 w-2 h-2"
                      style={{ backgroundColor: getColorForArtist(artist.name, index + 3) }}
                    />
                    <span
                      className="font-medium text-[11px] leading-tight whitespace-nowrap"
                      style={{ color: getColorForArtist(artist.name, index + 3) }}
                      title={artist.name}
                    >
                      {artist.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            artists.map((artist, index) => (
              <div key={artist._id} className="flex items-center gap-2">
                <div
                  className="rounded-full flex-shrink-0 w-3 h-3"
                  style={{ backgroundColor: getColorForArtist(artist.name, index) }}
                />
                <span
                  className="font-medium text-base"
                  style={{ color: getColorForArtist(artist.name, index) }}
                >
                  {artist.name}
                </span>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ComparisonOfArtistProfitability;
