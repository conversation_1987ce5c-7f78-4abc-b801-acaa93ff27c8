"use client";

import { Artist } from "../../../types/artist";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import { FiInfo } from "react-icons/fi";

interface ArtistSalesPerformanceProps {
  artists: Artist[];
  dictionary: {
    title: string;
    turnover: string;
    lots_sold: string;
    record_price: string;
    description?: string;
    turnover_year: string;
    lots_sold_year: string;
    record_adjudication: string;
  };
}

const ArtistSalesPerformance: React.FC<ArtistSalesPerformanceProps> = ({
  artists,
  dictionary,
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const getLatestMetric = (artist: Artist) => {
    if (!artist.metrics || artist.metrics.length === 0) return null;
    return artist.metrics.reduce((latest, metric) => {
      return parseInt(metric.year) > parseInt(latest.year) ? metric : latest;
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("en-US").format(value);
  };

  const description = dictionary.description ||
    "Cette rubrique correspond au record de vente, ainsi qu'au chiffre d'affaires et au nombre d'œuvres vendues par artiste sur la dernière année";

  // Predefined colors for specific artists to match the design
  const artistColors: Record<string, string> = {
    "Jean-Michel Basquiat": "var(--artist-basquiat)",
    "Pierre Soulages": "var(--artist-soulages)",
    "Andy Warhol": "var(--artist-warhol)",
    "Pablo PICASSO": "var(--artist-picasso)",
    "Zao Wou-ki": "var(--artist-zao)",
    "David Hockney": "var(--artist-hockney)",
  };

  // Fallback colors for any additional artists
  const fallbackColors = [
    "var(--fallback-1)",
    "var(--fallback-2)",
    "var(--fallback-3)",
    "var(--fallback-4)",
    "var(--fallback-5)",
    "var(--fallback-6)",
    "var(--fallback-7)",
  ];

  const getColorForArtist = (artistName: string, index: number) => {
    return (
      artistColors[artistName] || fallbackColors[index % fallbackColors.length]
    );
  };

  return (
    <div className="bg-[#F5F5F5] py-22 mt-8 w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw]">
      <div className="max-w-none mx-auto px-6 sm:px-6 lg:px-10 xl:px-32">

        <div className={`text-center ${isMobile ? 'mb-4 mt-4' : 'mb-20'}`}>
          <h1 className={`font-prata text-black ${isMobile ? 'text-3xl mb-2' : 'mb-3'}`} style={isMobile ? {} : { fontSize: '45px' }}>
            {dictionary.title}
            <FiInfo className={`inline-block ml-2 text-black ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} style={{ verticalAlign: 'super' }} />
          </h1>
          <p className={`text-black mx-auto leading-relaxed font-karla ${isMobile ? 'max-w-full text-sm px-1' : 'max-w-4xl'}`} style={isMobile ? {} : { fontSize: '20px' }}>
            {description}
          </p>
        </div>

        <div className={`grid ${isMobile ? 'grid-cols-1 gap-6' : 'grid-cols-3 gap-0'} relative`}>
          {artists.map((artist, index) => {
            const latestMetric = getLatestMetric(artist);
            const artistColor = getColorForArtist(artist.name, index);
            const year = latestMetric ? latestMetric.year : "2023";

            return (
              <div key={artist._id} className={`relative ${isMobile ? 'px-4 py-4' : 'px-6 py-8'}`}>
                {/* Artist Content - with spacing from intersection dividers */}
                <div className="-mt-4 -ml-1 mb-15">
                  {/* Artist Header */}
                  <div className={`flex items-center font-karla ${isMobile ? 'mb-4' : 'mb-8'}`}>
                    <div
                      className="w-16 h-16 rounded-full mr-4 flex items-center justify-center overflow-hidden border-[5px] relative"
                      style={{ backgroundColor: artistColor, borderColor: artistColor }}
                    >
                      {artist.image ? (
                        <Image
                          src={`${process.env.NEXT_PUBLIC_BACKEND_BASE_URL}${artist.image}`}
                          alt={artist.name}
                          fill
                          className="rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-white text-2xl font-bold">
                          {artist.name.charAt(0)}
                        </span>
                      )}
                    </div>
                    <div>
                      <h3
                        className="text-[20px] font-semibold font-karla whitespace-nowrap"
                        style={{ color: artistColor }}
                      >
                        {artist.name}
                      </h3>
                    </div>
                  </div>

                  {/* Artist Metrics */}
                  {latestMetric && (
                    <div className="space-y-1 text-gray-900 font-karla ">
                      <div className="flex justify-between items-center text-base">
                        <span >
                          {dictionary.turnover_year.replace('{year}', year)}
                        </span>
                        <span className="text-black font-semibold">
                          {formatCurrency(
                            latestMetric.total_auction_sales.find(
                              (p) => p.curr === "USD"
                            )?.value || 0
                          )}
                        </span>
                      </div>

                      {/* Divider line */}
                      <div className="border-t border-gray-200 "></div>

                      <div className="flex justify-between items-center text-base">
                        <span>
                          {dictionary.lots_sold_year.replace('{year}', year)}
                        </span>
                        <span className="text-black font-semibold">
                          {formatNumber(latestMetric.lots_sold)}
                        </span>
                      </div>

                      {/* Divider line */}
                      <div className="border-t border-gray-200"></div>

                      <div className="flex justify-between items-center text-base">
                        <span>
                          {dictionary.record_adjudication}
                        </span>
                        <span className="text-black font-semibold">
                          {formatCurrency(
                            latestMetric.all_time_record_price.find(
                              (p) => p.curr === "USD"
                            )?.value || 0
                          )}
                        </span>
                      </div>

                    </div>
                  )}
                </div>


                {/* Enhanced Intersection Cross (+) with extending lines - With gaps */}
                {!isMobile && index % 3 !== 2 && index < 3 && (
                  <div className="absolute right-0 bottom-8 w-16 h-16 z-10 transform translate-x-1/2 translate-y-1/2">
                    {/* Lines extending to edges with gaps around center + */}

                    {/* Vertical extending lines - with increased gap from cross */}
                    <div className="absolute top-0 left-1/2 w-px bg-gray-300 z-0"
                      style={{ height: '205px', transform: 'translateX(-50%) translateY(-240px)' }} />
                    <div className="absolute bottom-0 left-1/2 w-px bg-gray-300 z-0"
                      style={{ height: '205px', transform: 'translateX(-50%) translateY(243px)' }} />

                    {/* Horizontal extending lines - user's exact measurements with lower z-index */}
                    <div className="absolute left-0 top-1/2 h-px bg-gray-300 z-5"
                      style={{ width: '360px', transform: 'translateY(-50%) translateX(-395px)' }} />
                    <div className="absolute right-0 top-1/2 h-px bg-gray-300 z-5"
                      style={{ width: '352px', transform: 'translateY(-50%) translateX(387px)' }} />

                    {/* Middle horizontal divider - segmented with gaps around crosses */}
                    {index === 0 && (
                      <>
                        {/* First segment: from first cross to middle */}
                        <div className="absolute left-1/2 top-1/2 h-px bg-gray-300 z-20"
                          style={{ width: '80px', transform: 'translateY(-50%) translateX(50px)' }} />
                        {/* Second segment: from middle to second cross */}
                        <div className="absolute left-1/2 top-1/2 h-px bg-gray-300 z-20"
                          style={{ width: '80px', transform: 'translateY(-50%) translateX(170px)' }} />
                      </>
                    )}

                    {/* Center + symbol in BLACK - bigger */}
                    <div className="absolute left-1/2 top-1/2 z-10 transform -translate-x-1/2 -translate-y-1/2">
                      {/* Vertical part of + in black */}
                      <div className="absolute w-px h-24 bg-black left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                      {/* Horizontal part of + in black */}
                      <div className="absolute w-24 h-px bg-black left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ArtistSalesPerformance;
