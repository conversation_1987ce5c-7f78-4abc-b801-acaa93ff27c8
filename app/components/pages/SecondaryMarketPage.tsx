interface SecondaryMarketPageProps {
  lang: "en" | "fr";
}

export default function SecondaryMarketPage({ lang }: SecondaryMarketPageProps) {
  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {lang === "fr" ? "Marché Secondaire" : "Secondary Market"}
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {lang === "fr"
              ? "Découvrez et achetez des parts d'œuvres d'art sur notre marché secondaire"
              : "Discover and purchase shares of artworks on our secondary market"
            }
          </p>
        </div>

        {/* Coming Soon Section */}
        <div className="text-center py-16">
          <p className="text-xl text-gray-500 mb-4">
            {lang === "fr"
              ? "Le Marché Secondaire sera bientôt disponible."
              : "The Secondary Market will be available soon."
            }
          </p>
          <p className="text-gray-600">
            {lang === "fr"
              ? "Nous travaillons dur pour vous apporter une place de marché passionnante pour échanger des parts d'art."
              : "We're working hard to bring you an exciting marketplace for trading art shares."
            }
          </p>
        </div>
      </div>
    </div>
  );
}
