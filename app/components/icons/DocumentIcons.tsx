// Document SVG Icons for Primary Market - Using actual SVG files from public folder
import Image from "next/image";

interface IconProps {
  className?: string;
}

// Prospectus Réglementaire
export const ProspectusIcon = ({ className = "w-6 h-6" }: IconProps) => (
  <Image
    src="/images/primary-market/Prospectus.svg"
    alt="Prospectus"
    width={24}
    height={24}
    className={className}
  />
);

// Document Technique
export const TechnicalDocumentIcon = ({ className = "w-6 h-6" }: IconProps) => (
  <Image
    src="/images/primary-market/Document.svg"
    alt="Document Technique"
    width={24}
    height={24}
    className={className}
  />
);

// Attestation d'authenticité
export const AuthenticationIcon = ({ className = "w-6 h-6" }: IconProps) => (
  <Image
    src="/images/primary-market/Attestation.svg"
    alt="Attestation"
    width={24}
    height={24}
    className={className}
  />
);

// Titre de propriété
export const PropertyTitleIcon = ({ className = "w-6 h-6" }: IconProps) => (
  <Image
    src="/images/primary-market/Titre.svg"
    alt="Titre de propriété"
    width={24}
    height={24}
    className={className}
  />
);

// Notice
export const NoticeIcon = ({ className = "w-6 h-6" }: IconProps) => (
  <Image
    src="/images/primary-market/Notice.svg"
    alt="Notice"
    width={24}
    height={24}
    className={className}
  />
);
