import { PaintingData, ApiResponse } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { getDocumentUrl } from "../../utils/market";
import {
  ProspectusIcon,
  TechnicalDocumentIcon,
  AuthenticationIcon,
  PropertyTitleIcon,
  NoticeIcon
} from "../icons/DocumentIcons";

interface DocumentLinksProps {
  painting: PaintingData;
  artworkData: ApiResponse;
  lang: string;
}

interface DocumentLinkProps {
  url: string | null;
  icon: React.ReactNode;
  label: string;
  subLabel?: string;
}

const DocumentLink = ({ url, icon, label, subLabel }: DocumentLinkProps) => {
  if (!url) return null;

  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className="flex flex-col items-center hover:opacity-80 transition-opacity group"
    >
      <div className="mb-2 flex-shrink-0">
        {icon}
      </div>
      <div
        className="font-karla text-center text-gray-600 w-full max-w-20 sm:max-w-24"
        style={{
          fontSize: TYPOGRAPHY.documentLabel.fontSize,
          fontWeight: TYPOGRAPHY.documentLabel.fontWeight,
          lineHeight: TYPOGRAPHY.documentLabel.lineHeight,
          letterSpacing: '0%',
          opacity: 1
        }}
      >
        <div className="text-xs sm:text-sm leading-tight">{label}</div>
        {subLabel && <div className="text-xs sm:text-sm leading-tight">{subLabel}</div>}
      </div>
    </a>
  );
};

export const DocumentLinks = ({ painting, artworkData, lang }: DocumentLinksProps) => {
  const documents = [
    {
      url: getDocumentUrl(
        artworkData.base_url,
        undefined,
        lang,
        painting.client_pdf_fr,
        painting.client_pdf_en
      ),
      icon: <ProspectusIcon className="w-8 h-8 sm:w-10 sm:h-10" />,
      label: "Prospectus",
      subLabel: "Réglementaire",
    },
    {
      url: getDocumentUrl(
        artworkData.base_url,
        undefined,
        lang,
        painting.technical_file_fr,
        painting.technical_file_en
      ),
      icon: <TechnicalDocumentIcon className="w-8 h-8 sm:w-10 sm:h-10" />,
      label: "Document",
      subLabel: "Technique",
    },
    {
      url: getDocumentUrl(
        artworkData.base_url,
        undefined,
        lang,
        painting.auth_certif_fr,
        painting.auth_certif_en
      ),
      icon: <AuthenticationIcon className="w-8 h-8 sm:w-10 sm:h-10" />,
      label: "Attestation",
      subLabel: "d'authenticité",
    },
    {
      url: getDocumentUrl(
        artworkData.base_url,
        undefined,
        lang,
        painting.property_title_fr,
        painting.property_title_en
      ),
      icon: <PropertyTitleIcon className="w-8 h-8 sm:w-10 sm:h-10" />,
      label: "Titre de",
      subLabel: "propriété",
    },
    {
      url: getDocumentUrl(
        artworkData.base_url,
        undefined,
        lang,
        painting.notice_fr,
        painting.notice_en
      ),
      icon: <NoticeIcon className="w-8 h-8 sm:w-10 sm:h-10" />,
      label: "Notice",
    },
  ];

  return (
    <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 sm:gap-6 lg:gap-8 justify-items-center">
        {documents.map((doc, index) => (
          <DocumentLink
            key={index}
            url={doc.url}
            icon={doc.icon}
            label={doc.label}
            subLabel={doc.subLabel}
          />
        ))}
      </div>
    </div>
  );
};
