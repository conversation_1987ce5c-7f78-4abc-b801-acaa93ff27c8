import { PaintingData } from "../../types/market";
import { Dictionary } from "../../lib/get-translation";

interface PriceInformationProps {
  painting: PaintingData;
  dictionary: Dictionary;
  lang: string;
}

export const PriceInformation = ({ painting, dictionary, lang }: PriceInformationProps) => {
  return (
    <div className="font-karla font-normal text-gray-600">
      <div className="space-y-2 mb-6">
        {/* First Line - Artwork Share Price */}
        <div className="flex justify-between items-center">
          <span className="text-black font-bold text-[16px] leading-[28px]">
            – {dictionary.primary_market?.artwork_share_price || (lang === 'en' ? 'Artwork Share Price' : 'Cote Part de l\'Œuvre')} :
          </span>
          <span className="text-black text-[16px] leading-[28px] font-normal">
            {painting.purchase_price_per_part.value} $
          </span>
        </div>

        {/* Second Line - Artist Share Price */}
        <div className="flex justify-between items-center">
          <span className="text-[#B38642] font-normal text-[16px] leading-[28px]">
            – {dictionary.primary_market?.artist_share_price || (lang === 'en' ? 'Artist Share Price' : 'Cote Part de l\'Artiste')} :
          </span>
          <span className="text-[#B38642] text-[16px] leading-[28px] font-normal">
            {painting.artist?.ic_artist_usd ?? painting.price_per_part.value} $ /Part
          </span>
        </div>
      </div>
    </div>
  );
};
