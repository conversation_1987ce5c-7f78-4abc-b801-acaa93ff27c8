import Image from "next/image";
import { PaintingData, ApiResponse } from "../../types/market";
import { IMAGE_SIZES, TYPOGRAPHY } from "../../constants/market";
import { buildImageUrl, handleImageError } from "../../utils/market";

interface CertificateSectionProps {
  painting: PaintingData;
  artworkData: ApiResponse;
  lang: string;
}

export const CertificateSection = ({ painting, artworkData, lang }: CertificateSectionProps) => {
  return (
    <div>
      <h3
        className="font-prata text-gray-800 mb-8 text-center mx-auto whitespace-nowrap"
        style={{
          width: TYPOGRAPHY.certificateTitle.width,
          height: TYPOGRAPHY.certificateTitle.height,
          fontSize: TYPOGRAPHY.certificateTitle.fontSize,
          fontWeight: TYPOGRAPHY.certificateTitle.fontWeight,
          lineHeight: TYPOGRAPHY.certificateTitle.lineHeight,
          letterSpacing: '0%',
          opacity: 1
        }}
      >
        {lang === 'en' ? 'Your Certificate' : 'Votre Certificat'}
      </h3>
      <div className="flex justify-center">
        {painting.pr_certif_img ? (
          <div className="relative">
            <Image
              src={buildImageUrl(artworkData.base_url, painting.pr_certif_img)}
              alt={lang === 'en' ? 'Certificate' : 'Certificat'}
              width={IMAGE_SIZES.certificate.width}
              height={IMAGE_SIZES.certificate.height}
              className="rounded-lg shadow-2xl"
              onError={() => {
                handleImageError(buildImageUrl(artworkData.base_url, painting.pr_certif_img!));
              }}
            />
          </div>
        ) : (
          <div
            className="bg-gray-100 rounded-lg shadow-2xl flex items-center justify-center"
            style={{
              width: `${IMAGE_SIZES.certificate.width}px`,
              height: `${IMAGE_SIZES.certificate.height}px`,
              opacity: 1
            }}
          >
            <span className="text-gray-400">
              {lang === 'en' ? 'Certificate not available' : 'Certificat non disponible'}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
