import React from 'react';
import { Dictionary } from '../../lib/get-translation';
import { PaintingData } from '../../types/market';

interface SharesInformationProps {
  painting: PaintingData;
  dictionary?: Dictionary;
  lang: string;
}

export function SharesInformation({ painting, lang }: SharesInformationProps) {
  // Safe property access with fallbacks
  const totalParts = painting?.total_part || 0;
  const partPrice = painting?.purchase_price_per_part?.value || 0;
  const currency = painting?.purchase_price_per_part?.curr || 'USD';

  return (
    <div className="space-y-4">
      <div>
        <span>{lang === 'en' ? 'Total Shares' : 'Parts Totales'}: </span>
        <span>{totalParts.toLocaleString()}</span>
      </div>
      <div>
        <span>{lang === 'en' ? 'Price per Share' : 'Prix par Part'}: </span>
        <span>{partPrice.toLocaleString()} {currency}</span>
      </div>
    </div>
  );
}
