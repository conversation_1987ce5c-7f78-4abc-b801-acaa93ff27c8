import { PaintingData } from "../../types/market";
import { Dictionary } from "../../lib/get-translation";

interface ArtistInfoProps {
  painting: PaintingData;
  dictionary: Dictionary;
}

export function ArtistInfo({ painting }: ArtistInfoProps) {
  // Safe property access with fallbacks
  const artistName = painting?.artist?.name || 'Unknown Artist';
  const artistBio = painting?.artist?.bio || 'No biography available';
  const nationality = painting?.artist?.nationality || 'Unknown';
  const birthYear = painting?.artist?.life_dates?.date_of_birth || 'Unknown';

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold">{artistName}</h3>
      <p><strong>Nationality:</strong> {nationality}</p>
      <p><strong>Born:</strong> {birthYear}</p>
      <p className="text-gray-700">{artistBio}</p>
    </div>
  );
}
