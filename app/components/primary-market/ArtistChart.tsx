import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, <PERSON>Axi<PERSON>, <PERSON>A<PERSON><PERSON>, ResponsiveContainer } from "recharts";
import { PaintingData } from "../../types/market";

interface ChartDataPoint {
  year: string;
  artistRatingIndex: number | null;
  artistShareRating: number | null;
}

interface IndexItem {
  year: string;
  price?: Array<{ curr: string; value: string | number }>;
}

interface EvolutionItem {
  year: string;
  value: string | number;
}

interface ArtistChartProps {
  painting: PaintingData;
}

export const ArtistChart = React.memo(({ painting }: ArtistChartProps) => {
  const chartData = React.useMemo(() => {
    console.log('ArtistChart - painting data:', painting);
    console.log('ArtistChart - artist_evolution:', painting?.artist_evolution);
    console.log('ArtistChart - index_artist:', painting?.index_artist);

    // Create data for years 2000-2023
    const years = Array.from({ length: 24 }, (_, i) => (2000 + i).toString());
    console.log('ArtistChart - years range:', years);

    const data = years.map(year => {
      const dataPoint: ChartDataPoint = {
        year: year,
        artistRatingIndex: null,
        artistShareRating: null
      };

      // Artist Rating Index (gold/brown color) - from index_artist
      const indexData = painting?.index_artist?.find((item: IndexItem) => item.year === year);
      if (indexData) {
        const usdPrice = indexData.price?.find((p) => p.curr === "USD");
        dataPoint.artistRatingIndex = Number(usdPrice?.value) || null;
      }

      // Artist's Share Rating (black) - from artist_evolution
      const evolutionData = painting?.artist_evolution?.find((item: EvolutionItem) => item.year === year);
      if (evolutionData) {
        dataPoint.artistShareRating = Number(evolutionData.value) || null;
      }

      return dataPoint;
    });

    console.log('ArtistChart - final chartData:', data);
    return data;
  }, [painting]);

  return (
    <div className="p-15">
      <div className="h-120 w-full" style={{ pointerEvents: 'none' }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            style={{ outline: 'none' }}
          >
            <XAxis
              dataKey="year"
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb', strokeWidth: 1 }}
              interval={0}
              angle={0}
              textAnchor="middle"
            />

            <YAxis
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb', strokeWidth: 1 }}
              domain={[0, 300]}
              ticks={[0, 75, 150, 225, 300]}
            />

            {/* Artist Rating Index Line - Gold/Brown Color */}
            <Line
              type="monotone"
              dataKey="artistRatingIndex"
              stroke="#B8860B"
              strokeWidth={2}
              dot={false}
              activeDot={false}
              connectNulls={false}
              style={{ outline: 'none' }}
            />

            {/* Artist's Share Rating Line - Black */}
            <Line
              type="monotone"
              dataKey="artistShareRating"
              stroke="#000000"
              strokeWidth={2}
              dot={false}
              activeDot={false}
              connectNulls={false}
              style={{ outline: 'none' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Legend with French labels */}
      <div className="flex justify-center gap-8 mt-6">
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: '#B8860B' }}
          ></div>
          <span className="text-sm font-karla text-gray-700">
            Indice de Cotation d&apos;Artiste
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full bg-black"
          ></div>
          <span className="text-sm font-karla text-gray-700">
            Cote Part de l&apos;Artiste
          </span>
        </div>
      </div>
    </div>
  );
});

ArtistChart.displayName = 'ArtistChart';