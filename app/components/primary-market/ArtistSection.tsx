import React from 'react';
import Image from 'next/image';
import { PaintingData } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { getLocalizedText, buildImageUrl, formatLifeDates } from "../../utils/market";

interface ArtistSectionProps {
  painting: PaintingData;
  lang: string;
}

export const ArtistSection: React.FC<ArtistSectionProps> = ({ painting, lang }) => {
  return (
    <div className="w-full bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-20">
        {/* Centered Title */}
        <div className="text-center mb-12">
          <h1
            className="font-prata"
            style={{
              fontSize: '40px',
              fontWeight: 400,
              lineHeight: '48px',
              color: '#111827',
            }}
          >
            {painting.artist?.name || 'Pablo Picasso'}, {lang === 'en' ? 'his life & his work' : 'sa vie & son œuvre'}
          </h1>
        </div>

        {/* Main Content - Artist Info and Image */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-start">
          {/* Left side - Artist Information */}
          <div className="flex flex-col justify-start">
            {/* Artist Name with Dates */}
            <h2
              className="font-karla mb-6"
              style={{
                fontSize: TYPOGRAPHY.artistSectionName.fontSize,
                fontWeight: TYPOGRAPHY.artistSectionName.fontWeight,
                lineHeight: TYPOGRAPHY.artistSectionName.lineHeight,
                color: TYPOGRAPHY.artistSectionName.color,
                textAlign: 'justify',
                letterSpacing: '0%',
              }}
            >
              {painting.artist?.name || 'Pablo PICASSO'} {painting.artist?.life_dates?.date_of_birth && painting.artist?.life_dates?.date_of_death && formatLifeDates(painting.artist.life_dates.date_of_birth, painting.artist.life_dates.date_of_death)}
            </h2>

            {/* Artist Description */}
            {painting.artist?.description && (
              <div
                className="font-karla"
                style={{
                  fontSize: TYPOGRAPHY.artistSectionDescription.fontSize,
                  fontWeight: TYPOGRAPHY.artistSectionDescription.fontWeight,
                  lineHeight: TYPOGRAPHY.artistSectionDescription.lineHeight,
                  color: TYPOGRAPHY.artistSectionDescription.color,
                  textAlign: 'justify',
                  letterSpacing: '0%',
                }}
              >
                {getLocalizedText(painting.artist.description, lang)}
              </div>
            )}
          </div>

          {/* Right side - Artist Image */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative w-full max-w-sm aspect-[3/4] bg-gray-100 shadow-lg">
              {painting.artist?.image ? (
                <Image
                  src={buildImageUrl(process.env.NEXT_PUBLIC_BACKEND_BASE_URL || '', painting.artist.image)}
                  alt={painting.artist?.name || 'Artist'}
                  fill
                  className="object-cover"
                  priority
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">No artist image available</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};