import React from 'react';
import Image from 'next/image';
import { PaintingData } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { getLocalizedText, buildImageUrl, formatLifeDates } from "../../utils/market";

interface ArtistSectionProps {
  painting: PaintingData;
  lang: string;
}

export const ArtistSection: React.FC<ArtistSectionProps> = ({ painting, lang }) => {
  return (
    <div className="w-full bg-white">
      <div className="max-w-[1600px] mx-auto px-4 pt-20 pb-16">

        {/* Title - Centered */}
        <div className="mb-14 text-center">
          <h1
            className="font-prata"
            style={{
              ...TYPOGRAPHY.artistSectionTitle,
              fontSize: '40px',
              fontWeight: 400,
              lineHeight: '56px',
            }}
          >
            {painting.artist?.name
              ? painting.artist.name
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ')
              : 'Pablo Picasso'}, {lang === 'en' ? 'his life & his work' : 'sa vie & son œuvre'}
          </h1>
        </div>

        {/* Two-column layout */}
        <div className="grid grid-cols-1 lg:grid-cols-[800px_1fr] gap-20 items-start">

          {/* Left: Artist name + description */}
          <div>
            {/* Name + Dates */}
            <h2 className="font-karla mb-6 flex items-baseline gap-2 flex-wrap">
              <span style={TYPOGRAPHY.artistSectionName}>
                {painting.artist?.name || 'Pablo PICASSO'}
              </span>
              {painting.artist?.life_dates?.date_of_birth && painting.artist?.life_dates?.date_of_death && (
                <span style={TYPOGRAPHY.artworkArtistDates}>
                  {formatLifeDates(
                    painting.artist.life_dates.date_of_birth,
                    painting.artist.life_dates.date_of_death
                  )}
                </span>
              )}
            </h2>

            {/* Description */}
            {painting.artist?.description && (
              <div
                className="font-karla"
                style={{
                  width: '855px',
                  fontFamily: 'Karla',
                  fontWeight: 400,
                  fontSize: '16px',
                  lineHeight: '26px',
                  letterSpacing: '0%',
                  textAlign: 'justify',
                  color: TYPOGRAPHY.artistSectionDescription.color,
                }}
              >
                {getLocalizedText(painting.artist.description, lang)}
              </div>
            )}
          </div>

          {/* Right: Image */}
          <div className="flex justify-end">
            <div className="relative w-[500px] aspect-[16/9]"> {/* Wide-screen shape */}
              {painting.artist?.image ? (
                <Image
                  src={buildImageUrl(
                    process.env.NEXT_PUBLIC_BACKEND_BASE_URL || '',
                    painting.artist.image
                  )}
                  alt={painting.artist?.name || 'Artist'}
                  fill
                  className="object-contain" // keeps full image without cutting
                  priority
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">No artist image available</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};