import Image from "next/image";
import { PaintingData, ApiResponse } from "../../types/market";
import { IMAGE_SIZES } from "../../constants/market";
import { buildImageUrl, handleImageError } from "../../utils/market";

interface ArtworkImageProps {
  painting: PaintingData;
  artworkData: ApiResponse;
}

export const ArtworkImage = ({ painting, artworkData }: ArtworkImageProps) => {
  return (
    <div
      className="relative"
      style={{
        width: `${IMAGE_SIZES.artwork.width}px`,
        height: `${IMAGE_SIZES.artwork.height}px`,
        opacity: 1
      }}
    >
      <Image
        src={buildImageUrl(artworkData.base_url, painting.cover_img)}
        alt={`${painting.title.en} by ${painting.artist.name}`}
        width={IMAGE_SIZES.artwork.width}
        height={IMAGE_SIZES.artwork.height}
        className="rounded-sm shadow-lg object-cover"
        priority
        onError={() => {
          handleImageError(buildImageUrl(artworkData.base_url, painting.cover_img));
        }}
      />
    </div>
  );
};
