import React from 'react';
import { TYPOGRAPHY } from "../../constants/market";
import { ArtistChart } from "./ArtistChart";
import { PaintingData } from "../../types/market";

interface ArtistIndexAndShareProps {
  painting: PaintingData;
  lang: string;
}

export function ArtistIndexAndShare({ painting, lang }: ArtistIndexAndShareProps) {
  return (
    <div className="w-full bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-20">
        {/* Section Title */}
        <div className="text-center mb-12">
          <h2
            className="font-prata mb-4"
            style={{
              fontSize: TYPOGRAPHY.artistIndexTitle.fontSize,
              fontWeight: TYPOGRAPHY.artistIndexTitle.fontWeight,
              lineHeight: TYPOGRAPHY.artistIndexTitle.lineHeight,
              color: TYPOGRAPHY.artistIndexTitle.color,
            }}
          >
            {lang === 'en' ? 'Artist Rating Index & Artist Share Rating' : 'Indice de Cotation Artiste & Cote Part Artiste'}
          </h2>
        </div>

        {/* Chart Section */}
        <div className="">
          <ArtistChart painting={painting} />
        </div>
      </div>
    </div>
  );
}
