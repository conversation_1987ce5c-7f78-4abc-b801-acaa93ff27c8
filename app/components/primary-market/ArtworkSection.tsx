import React from 'react';
import { PaintingData } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { getLocalizedText, buildImageUrl } from "../../utils/market";
import Image from 'next/image';

interface ArtworkSectionProps {
  painting: PaintingData;
  lang: string;
}

export const ArtworkSection: React.FC<ArtworkSectionProps> = ({ painting, lang }) => {
  return (
    <div
      className="w-full"
      style={{
        backgroundColor: TYPOGRAPHY.artworkSectionLayout.backgroundColor,
        opacity: 1
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-20">
        {/* Title Section */}
        <div className="text-center mb-12 lg:mb-16">
          <h1
            className="font-prata text-3xl sm:text-4xl lg:text-5xl"
            style={{
              fontWeight: TYPOGRAPHY.artworkSectionTitle.fontWeight,
              color: TYPOGRAPHY.artworkSectionTitle.color,
              textAlign: 'center',
            }}
          >
            {getLocalizedText(painting.title, lang)}
          </h1>
        </div>

        {/* Two-column layout */}
        <div className="grid grid-cols-1 lg:grid-cols-[800px_1fr] gap-20 items-start">

          {/* Left: Image */}
          <div className="flex justify-start">
            <div
              className="relative bg-white shadow-lg w-[500px]"
              style={{
                aspectRatio: '509/282',
                opacity: 1
              }}
            >
              {painting.pr_cover_img ? (
                <Image
                  src={buildImageUrl(process.env.NEXT_PUBLIC_BACKEND_BASE_URL || '', painting.pr_cover_img)}
                  alt={getLocalizedText(painting.title, lang) || 'Artwork'}
                  fill
                  className="object-cover"
                  priority
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">No image available</span>
                </div>
              )}
            </div>
          </div>

          {/* Right: Description */}
          <div>
            {painting.description && (
              <p
                className="font-karla"
                style={{
                  fontSize: TYPOGRAPHY.artworkDescription.fontSize,
                  fontWeight: TYPOGRAPHY.artworkDescription.fontWeight,
                  lineHeight: TYPOGRAPHY.artworkDescription.lineHeight,
                  color: TYPOGRAPHY.artworkDescription.color,
                  textAlign: 'justify',
                }}
              >
                {getLocalizedText(painting.description, lang)}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};