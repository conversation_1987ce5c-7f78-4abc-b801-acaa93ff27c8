import React from 'react';
import { PaintingData } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { getLocalizedText, buildImageUrl } from "../../utils/market";
import Image from 'next/image';

interface ArtworkSectionProps {
  painting: PaintingData;
  lang: string;
}

export const ArtworkSection: React.FC<ArtworkSectionProps> = ({ painting, lang }) => {
  return (
    <div
      className="w-full"
      style={{
        backgroundColor: TYPOGRAPHY.artworkSectionLayout.backgroundColor,
        opacity: 1,
      }}
    >
      <div className="max-w-[1600px] mx-auto px-4 py-16 lg:py-20">

        {/* Title Section - Centered */}
        <div className="text-center mb-12 lg:mb-16">
          <h1
            className="font-prata text-3xl sm:text-4xl lg:text-5xl"
            style={{
              fontWeight: TYPOGRAPHY.artworkSectionTitle.fontWeight,
              color: TYPOGRAPHY.artworkSectionTitle.color,
              textAlign: 'center',
            }}
          >
            {getLocalizedText(painting.title, lang)}
          </h1>
        </div>

        {/* Flex layout for precise positioning */}
        <div className="flex items-start" style={{ marginLeft: '120px' }}>

          {/* Image with exact dimensions */}
          <div
            className="relative bg-white shadow-lg flex-shrink-0"
            style={{
              width: '509px',
              height: '282px',
              opacity: 1
            }}
          >
            {painting.pr_cover_img ? (
              <Image
                src={buildImageUrl(process.env.NEXT_PUBLIC_BACKEND_BASE_URL || '', painting.pr_cover_img)}
                alt={getLocalizedText(painting.title, lang) || 'Artwork'}
                fill
                className="object-cover"
                priority
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500">No image available</span>
              </div>
            )}
          </div>

          {/* Description aligned next to image */}
          <div style={{ marginLeft: '41px' }}> {/* 670px - 509px - 120px = 41px gap */}
            {painting.description && (
              <div>
                {/* First part - title and date */}
                <p
                  className="font-karla"
                  style={{
                    width: '810px',
                    height: '32px',
                    fontFamily: 'Karla',
                    fontWeight: 600,
                    fontSize: '18px',
                    lineHeight: '32px',
                    letterSpacing: '0%',
                    textAlign: 'justify',
                    textTransform: 'uppercase',
                    color: TYPOGRAPHY.artworkDescription.color,
                    marginBottom: '16px'
                  }}
                >
                  Cette œuvre intitulée "Tête d'homme barbu", réalisée par Pablo Picasso le 12 août 1963
                </p>

                {/* Rest of description */}
                <p
                  className="font-karla"
                  style={{
                    fontSize: TYPOGRAPHY.artworkDescription.fontSize,
                    fontWeight: TYPOGRAPHY.artworkDescription.fontWeight,
                    lineHeight: TYPOGRAPHY.artworkDescription.lineHeight,
                    color: TYPOGRAPHY.artworkDescription.color,
                    textAlign: 'justify',
                  }}
                >
                  {(() => {
                    const fullText = getLocalizedText(painting.description, lang) || '';
                    const firstPart = 'Cette œuvre intitulée "Tête d\'homme barbu", réalisée par Pablo Picasso le 12 août 1963, ';
                    return fullText.replace(firstPart, '');
                  })()}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
