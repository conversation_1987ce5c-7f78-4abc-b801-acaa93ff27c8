import { PaintingData } from "../../types/market";
import { getLocalizedText } from "../../utils/market";

interface ArtworkDetailsProps {
  painting: PaintingData;
  lang: string;
}

export function ArtworkDetails({ painting, lang }: ArtworkDetailsProps) {
  // Safe property access using the utility function for localized text
  const title = getLocalizedText(painting?.title, lang) || 'Untitled';
  const artistName = painting?.artist?.name || 'Unknown Artist';
  const year = painting?.year?.toString() || 'Unknown';
  // Fix: use dimension (singular) instead of dimensions (plural)
  const dimensions = painting?.dimension ? `${painting.dimension.height}x${painting.dimension.width}` : 'Not specified';
  // Fix: technique isn't in the PaintingData type, we can use painting_type instead
  const technique = painting?.painting_type_en || (lang === 'fr' ? painting?.painting_type_fr : '') || 'Not specified';

  return (
    <div className="space-y-3">
      <h2 className="text-2xl font-bold">{title}</h2>
      <p className="text-lg">{artistName}</p>
      <p>{year}</p>
      <p>{dimensions}</p>
      <p>{technique}</p>
    </div>
  );
}
