import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON>s, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";
import { PaintingData } from "../../types/market";
import { Dictionary } from "../../lib/get-translation";

interface PriceChartProps {
  painting: PaintingData;
  dictionary: Dictionary;
}

export function PriceChart({ painting }: PriceChartProps) {
  // Add fallback data to prevent build errors
  const chartData = React.useMemo(() => {
    if (!painting?.artist_evolution || !Array.isArray(painting.artist_evolution)) {
      return [
        { year: '2020', value: 100 },
        { year: '2021', value: 120 },
        { year: '2022', value: 110 },
        { year: '2023', value: 140 },
      ];
    }

    return painting.artist_evolution.map(item => ({
      year: item?.year || '',
      value: Number(item?.value) || 0
    }));
  }, [painting?.artist_evolution]);

  return (
    <div className="">
      <div className="h-64 w-full" style={{ pointerEvents: 'none' }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={chartData}
            style={{ outline: 'none' }}
          >
            <XAxis
              dataKey="year"
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />

            <YAxis
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              domain={[0, 600]}
              ticks={[0, 150, 300, 450, 600]}
            />

            <Line
              type="monotone"
              dataKey="artworkPrice"
              stroke="#1f2937"
              strokeWidth={2}
              dot={false}
              activeDot={false}
              style={{ outline: 'none' }}
            />

            <Line
              type="monotone"
              dataKey="artistPrice"
              stroke="#b38642"
              strokeWidth={2}
              dot={false}
              activeDot={false}
              style={{ outline: 'none' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

PriceChart.displayName = 'PriceChart';
