import { PaintingData } from "../../types/market";
import { TYPOGRAPHY } from "../../constants/market";
import { getLocalizedText } from "../../utils/market";

interface ArgumentsSectionProps {
  painting: PaintingData;
  lang: string;
}

export const ArgumentsSection = ({ painting, lang }: ArgumentsSectionProps) => {
  return (
    <div>
      {/* Title */}
      <h3
        className="font-prata text-gray-800 mb-16 text-center mx-auto whitespace-nowrap"
        style={{
          width: TYPOGRAPHY.argumentsTitle.width,
          height: TYPOGRAPHY.argumentsTitle.height,
          fontSize: TYPOGRAPHY.argumentsTitle.fontSize,
          fontWeight: TYPOGRAPHY.argumentsTitle.fontWeight,
          lineHeight: TYPOGRAPHY.argumentsTitle.lineHeight,
          letterSpacing: '0%',
          opacity: 1,
        }}
      >
        {lang === 'en' ? 'Our Arguments' : 'Nos Arguments'}
      </h3>

      {/* Grid Layout */}
      <div className="relative grid grid-cols-2 grid-rows-2 gap-y-24 gap-x-16 max-w-5xl mx-auto text-center">
        {painting.like_options.slice(0, 4).map((option, index) => (
          <div
            key={index}
            className="flex flex-col items-center justify-center px-6 z-10"
          >
            <div
              className="font-prata mb-4"
              style={{
                fontSize: TYPOGRAPHY.argumentsNumber.fontSize,
                fontWeight: TYPOGRAPHY.argumentsNumber.fontWeight,
                lineHeight: TYPOGRAPHY.argumentsNumber.lineHeight,
                color: TYPOGRAPHY.argumentsNumber.color, // should be #b38642
                height: TYPOGRAPHY.argumentsNumber.height,
              }}
            >
              {String(index + 1).padStart(2, '0')}
            </div>
            <p
              style={{
                fontSize: TYPOGRAPHY.argumentsText.fontSize,
                lineHeight: TYPOGRAPHY.argumentsText.lineHeight,
                fontFamily: TYPOGRAPHY.argumentsText.fontFamily,
                color: TYPOGRAPHY.argumentsText.color,
              }}
            >
              {getLocalizedText(option, lang)}
            </p>
          </div>
        ))}

        {/* Lines extending to edges with gaps around center + */}

        {/* Vertical Lines */}
        <div className="absolute top-0 left-1/2 w-px bg-black z-0" style={{ height: 'calc(50% - 35px)', transform: 'translateX(-50%)' }} />
        <div className="absolute bottom-0 left-1/2 w-px bg-black z-0" style={{ height: 'calc(50% - 35px)', transform: 'translateX(-50%)' }} />

        {/* Horizontal Lines */}
        <div className="absolute left-0 top-1/2 h-px bg-black z-0" style={{ width: 'calc(50% - 35px)', transform: 'translateY(-50%)' }} />
        <div className="absolute right-0 top-1/2 h-px bg-black z-0" style={{ width: 'calc(50% - 35px)', transform: 'translateY(-50%)' }} />

        {/* Longer and thinner + symbol in center */}
        <div className="absolute left-1/2 top-1/2 z-10" style={{ transform: 'translate(-50%, -50%)' }}>
          {/* Vertical part of + */}
          <div className="absolute bg-black" style={{ width: '1px', height: '48px', left: '50%', top: '50%', transform: 'translate(-50%, -50%)' }} />
          {/* Horizontal part of + */}
          <div className="absolute bg-black" style={{ width: '48px', height: '1px', left: '50%', top: '50%', transform: 'translate(-50%, -50%)' }} />
        </div>

      </div>
    </div>
  );
};
