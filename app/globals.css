@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-karla: 'Karla', sans-serif;
  --font-prata: 'Prata', serif;

  /* Artist Colors */
  --artist-basquiat: #1F77B4;
  --artist-soulages: #FF7F0E;
  --artist-warhol: #2CA02C;
  --artist-picasso: #D62728;
  --artist-zao: #9467BD;
  --artist-hockney: #8C564B;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

:root {
  --color-primary: #b9924f;

  /* Artist Colors */
  --artist-basquiat: #1E88E5;
  --artist-soulages: #FF9800;
  --artist-warhol: #4CAF50;
  --artist-picasso: #F44336;
  --artist-zao: #9C27B0;
  --artist-hockney: #795548;


  --fallback-1: #8884d8;
  --fallback-2: #82ca9d;
  --fallback-3: #ffc658;
  --fallback-4: #ff7300;
  --fallback-5: #00C49F;
  --fallback-6: #FFBB28;
  --fallback-7: #FF8042;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overscroll-behavior-y: none;
}

/* Font utility classes */
.font-karla {
  font-family: var(--font-karla);
}

.font-prata {
  font-family: var(--font-prata);
}

/* Loading animation styles */
@keyframes loading {
  0% {
    width: 0%;
    background-position: 0% 50%;
  }

  50% {
    width: 70%;
    background-position: 100% 50%;
  }

  100% {
    width: 100%;
    background-position: 0% 50%;
  }
}

.animate-loading-bar {
  animation: loading 2s ease-in-out infinite;
  background: linear-gradient(90deg, rgba(185, 146, 79, 0.8) 0%, rgba(185, 146, 79, 1) 50%, rgba(185, 146, 79, 0.8) 100%);
  background-size: 200% 200%;
}



/* 2. Position the button to contain the pseudo-element */
.custom-phone-input-container .react-international-phone-country-selector-button {
  position: relative !important;
  padding-right: 28px !important;
  /* Make space for the new arrow */
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

/* 3. Create and style the new arrow using ::after */
.custom-phone-input-container .react-international-phone-country-selector-button::after {
  content: '' !important;
  position: absolute !important;
  border: none !important;
  right: 8px !important;
  top: 50% !important;
  width: 12px !important;
  height: 12px !important;
  background-image: url('/arrow-left.svg') !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  background-size: contain !important;
  transform: translateY(-50%) !important;
  transition: transform 0.3s ease-in-out !important;
}

/* 4. Rotate the new arrow when the dropdown is open */
.custom-phone-input-container .react-international-phone-country-selector-button[aria-expanded="true"]::after {
  transform: translateY(-50%) rotate(180deg) !important;
}

.custom-phone-input-container .react-international-phone-input {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  padding-left: 0 !important;
}

.custom-phone-input-container {
  border-bottom: 1px solid #d1d5db !important;
  /* gray-300 */
  transition: border-color 0.2s ease-in-out;
  border-radius: 0 !important;
}

.custom-phone-input-container:focus-within {
  border-bottom-color: #000000 !important;
  /* black */
}