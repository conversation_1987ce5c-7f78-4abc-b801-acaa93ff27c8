export function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token');
}

export function getUserData(): Record<string, unknown> | null {
  if (typeof window === 'undefined') return null;
  const userData = localStorage.getItem('user_data');
  if (!userData || userData === 'undefined' || userData === 'null') {
    return null;
  }
  try {
    return JSON.parse(userData);
  } catch {
    return null;
  }
}

export function isAuthenticated(): boolean {
  return !!getAuthToken();
}

export function getRefreshToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('refresh_token');
}

export function setAuthData(token: string, user: Record<string, unknown>, refreshToken?: string): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem('auth_token', token);
  localStorage.setItem('user_data', JSON.stringify(user));
  if (refreshToken) {
    localStorage.setItem('refresh_token', refreshToken);
  }

  window.dispatchEvent(new Event('auth-change'));
}

export function clearAuth(): void {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('auth_token');
  localStorage.removeItem('user_data');
  localStorage.removeItem('refresh_token');

  window.dispatchEvent(new Event('auth-change'));
}
