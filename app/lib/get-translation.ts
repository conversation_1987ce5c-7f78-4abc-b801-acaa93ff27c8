import 'server-only';

// We can use this type to ensure all dictionaries have the same structure
export type Dictionary = typeof import('../locales/en.json');

const dictionaries = {
  en: () => import('../locales/en.json').then((module) => module.default),
  fr: () => import('../locales/fr.json').then((module) => module.default),
};

export const getDictionary = async (locale: 'en' | 'fr'): Promise<Dictionary> => {
  const loader = dictionaries[locale] || dictionaries.fr;
  return loader();
};
