import { ApiResponse } from '../../types/market';
import { API_ENDPOINTS } from '../../constants/market';

export class MarketApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public statusText?: string
  ) {
    super(message);
    this.name = 'MarketApiError';
  }
}

export const marketApi = {
  /**
   * Fetch primary market artwork data
   * @returns Promise<ApiResponse>
   * @throws MarketApiError
   */
  getPrimaryArtwork: async (): Promise<ApiResponse> => {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    if (!baseUrl) {
      throw new MarketApiError('API base URL is not configured');
    }

    const url = `${baseUrl}${API_ENDPOINTS.PRIMARY_MARKET}`;

    try {
      const response = await fetch(url, {
        next: { revalidate: 3600 }, // Revalidate every hour
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new MarketApiError(
          `Failed to fetch primary market data: ${response.statusText}`,
          response.status,
          response.statusText
        );
      }

      const data: ApiResponse = await response.json();

      // Validate response structure
      if (!data.paintings || !Array.isArray(data.paintings) || data.paintings.length === 0) {
        throw new MarketApiError('Invalid API response: No paintings found');
      }

      if (!data.base_url) {
        throw new MarketApiError('Invalid API response: Missing base_url');
      }

      return data;
    } catch (error) {
      if (error instanceof MarketApiError) {
        throw error;
      }

      // Handle network errors, JSON parsing errors, etc.
      throw new MarketApiError(
        `Network error while fetching primary market data: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  },

  /**
   * Build full image URL
   * @param baseUrl - Base URL from API response
   * @param imagePath - Relative image path
   * @returns Full image URL
   */
  buildImageUrl: (baseUrl: string, imagePath: string): string => {
    return `${baseUrl}${imagePath}`;
  },

  /**
   * Build artist image URL
   * @param imagePath - Relative image path
   * @returns Full artist image URL
   */
  buildArtistImageUrl: (imagePath: string): string => {
    const baseUrl = process.env.NEXT_PUBLIC_BACKEND_BASE_URL || '';
    return `${baseUrl}${imagePath}`;
  },
};
