import { Suspense } from "react";
import Header from "../components/header";
import Footer from "../components/footer";
import { getDictionary } from "../lib/get-translation";
import LoadingAnimation from "../components/LoadingAnimation";

// This type should ideally be shared from a single source
interface Kpi {
  _id: string;
  artist_name: string;
  value_euro: string;
  value_usd: string;
  percentage: string;
  direction: "up" | "down";
}

async function getKpis(): Promise<Kpi[]> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/artists/list/kpis`,
      { next: { revalidate: 3600 } }
    );
    if (!response.ok) {
      throw new Error('Failed to fetch KPIs');
    }
    const data = await response.json();
    return data.kpis as Kpi[];
  } catch (error) {
    console.error("Error fetching KPIs:", error);
    return [];
  }
}

export default async function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const lang = "en";
  const [kpis, dictionary] = await Promise.all([
    getKpis(),
    getDictionary(lang),
  ]);

  return (
    <>
      <Suspense fallback={null}>
        <LoadingAnimation />
      </Suspense>
      <Header kpis={kpis} dictionary={dictionary} lang={lang} />
      {children}
      <Footer lang={lang} />
    </>
  );
}
