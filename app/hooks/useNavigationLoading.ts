"use client";

import { useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";

export function useNavigationLoading() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  // This effect runs when the component mounts
  useEffect(() => {
    if (typeof window === "undefined") return;

    // Function to start loading state
    const startLoading = () => {
      setIsLoading(true);
    };

    // Function to end loading state with a delay for better UX
    const endLoading = () => {
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    };

    // Add event listeners for route changes
    window.addEventListener("beforeunload", startLoading);

    // Use MutationObserver as a fallback to detect DOM changes during navigation
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          // Check if this is a navigation-related change
          const isNavigationChange = Array.from(mutation.addedNodes).some(
            (node) => {
              if (node instanceof HTMLElement) {
                return (
                  node.tagName === "MAIN" ||
                  node.tagName === "DIV" ||
                  node.classList.contains("page-transition")
                );
              }
              return false;
            }
          );

          if (isNavigationChange) {
            startLoading();
            // Set a timeout to end loading after a minimum time
            setTimeout(endLoading, 500);
          }
        }
      }
    });

    // Start observing the document body for changes
    observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      window.removeEventListener("beforeunload", startLoading);
      observer.disconnect();
    };
  }, []);

  // This effect runs when the pathname or search params change
  useEffect(() => {
    setIsLoading(true);

    // Add a small delay to make the transition smoother
    const timeout = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timeout);
  }, [pathname, searchParams]);

  return isLoading;
}
