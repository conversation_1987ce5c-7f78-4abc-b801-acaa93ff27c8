"use client";

import { useState, useEffect } from "react";
import { isAuthenticated, getUserData } from "../lib/auth";

export function useAuth() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState<Record<string, unknown> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkAuth = () => {
    const authenticated = isAuthenticated();
    const userData = getUserData();

    setIsLoggedIn(authenticated);
    setUser(userData);
    setIsLoading(false);
  };

  useEffect(() => {
    // Check on mount
    checkAuth();


    const handleAuthChange = () => {
      checkAuth();
    };

    // Listen for storage changes (when user logs in/out in another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_token' || e.key === 'user_data') {
        checkAuth();
      }
    };

    window.addEventListener('auth-change', handleAuthChange);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('auth-change', handleAuthChange);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return { isLoggedIn, user, isLoading };
}