'use client';

import { useState, useCallback, useMemo } from 'react';
import { PaintingData } from '../types/market';
import { 
  calculateAvailableShares, 
  calculateSharesFromPercentage,
  calculateSharePercentage,
  isValidPercentage,
  logDebugInfo
} from '../utils/market';

export interface UsePrimaryMarketReturn {
  selectedPercentage: number;
  setSelectedPercentage: (percentage: number) => void;
  handlePercentageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  availableShares: number;
  selectedShares: number;
  sharePercentage: number;
  totalValue: number;
  isValidSelection: boolean;
}

export const usePrimaryMarket = (painting: PaintingData): UsePrimaryMarketReturn => {
  const [selectedPercentage, setSelectedPercentage] = useState<number>(0);

  // Debug logging in development
  useMemo(() => {
    logDebugInfo(painting);
  }, [painting]);

  // Calculate available shares
  const availableShares = useMemo(() => 
    calculateAvailableShares(painting.total_part, painting.sold_out_part),
    [painting.total_part, painting.sold_out_part]
  );

  // Calculate selected shares based on percentage
  const selectedShares = useMemo(() => 
    calculateSharesFromPercentage(selectedPercentage, painting.total_part, painting.sold_out_part),
    [selectedPercentage, painting.total_part, painting.sold_out_part]
  );

  // Calculate share percentage for progress bar
  const sharePercentage = useMemo(() => 
    calculateSharePercentage(availableShares, painting.total_part),
    [availableShares, painting.total_part]
  );

  // Calculate total value of selected shares
  const totalValue = useMemo(() => 
    selectedShares * painting.purchase_price_per_part.value,
    [selectedShares, painting.purchase_price_per_part.value]
  );

  // Validate selection
  const isValidSelection = useMemo(() => 
    isValidPercentage(selectedPercentage) && selectedShares > 0,
    [selectedPercentage, selectedShares]
  );

  // Handle percentage input change
  const handlePercentageChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (isValidPercentage(value)) {
      setSelectedPercentage(value);
    }
  }, []);

  return {
    selectedPercentage,
    setSelectedPercentage,
    handlePercentageChange,
    availableShares,
    selectedShares,
    sharePercentage,
    totalValue,
    isValidSelection,
  };
};
