import { PaintingData, EvolutionItem } from '../types/market';

// Price formatting utilities
export const formatPrice = (price: number, currency = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(price);
};

export const formatPriceSimple = (price: number): string => {
  return `$${price}`;
};

// Share calculations
export const calculateAvailableShares = (total: number, sold: number): number => {
  return Math.max(0, total - sold);
};

export const calculateSharePercentage = (available: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((available / total) * 100);
};

export const calculateSharesFromPercentage = (
  percentage: number,
  totalShares: number,
  soldShares: number
): number => {
  const availableShares = calculateAvailableShares(totalShares, soldShares);
  return Math.floor((percentage / 100) * availableShares);
};

// Date formatting utilities
export const formatLifeDates = (birthDate: string, deathDate: string): string => {
  const birthYear = new Date(birthDate).getFullYear();
  const deathYear = new Date(deathDate).getFullYear();
  return `(${birthYear} - ${deathYear})`;
};

export const formatYear = (dateString: string): string => {
  return new Date(dateString).getFullYear().toString();
};

// Chart data transformation
export const transformEvolutionData = (evolutionList: EvolutionItem[]) => {
  return evolutionList.map(item => ({
    year: item._id,
    price: item.totalValue,
  }));
};

// Image URL utilities
export const buildImageUrl = (baseUrl: string, imagePath: string): string => {
  return `${baseUrl}${imagePath}`;
};

export const buildArtistImageUrl = (imagePath: string): string => {
  const baseUrl = process.env.NEXT_PUBLIC_BACKEND_BASE_URL || '';
  return `${baseUrl}${imagePath}`;
};

// Document utilities
export const getDocumentUrl = (
  baseUrl: string,
  documentPath: string | undefined,
  lang: string,
  frPath?: string,
  enPath?: string
): string | null => {
  if (documentPath) {
    return buildImageUrl(baseUrl, documentPath);
  }
  
  if (lang === 'fr' && frPath) {
    return buildImageUrl(baseUrl, frPath);
  }
  
  if (lang === 'en' && enPath) {
    return buildImageUrl(baseUrl, enPath);
  }
  
  return null;
};

// Text utilities
export const getLocalizedText = (
  text: { en: string; fr: string },
  lang: string
): string => {
  return lang === 'fr' ? text.fr : text.en;
};

export const getPaintingType = (painting: PaintingData, lang: string): string => {
  return lang === 'fr' ? painting.painting_type_fr : painting.painting_type_en;
};

// Validation utilities
export const isValidPercentage = (value: number): boolean => {
  return !isNaN(value) && value >= 0 && value <= 100;
};

export const hasRequiredPaintingData = (painting: PaintingData): boolean => {
  return !!(
    painting._id &&
    painting.title &&
    painting.artist?.name &&
    painting.purchase_price_per_part?.value
  );
};

// Error handling utilities
export const handleImageError = (imagePath: string): void => {
  console.error('Failed to load image:', imagePath);
};

export const logDebugInfo = (painting: PaintingData): void => {
  if (process.env.NODE_ENV === 'development') {
    console.log('Painting data:', painting);
    console.log('Artist ic_artist_usd:', painting.artist?.ic_artist_usd);
    console.log('Available shares:', calculateAvailableShares(painting.total_part, painting.sold_out_part));
  }
};
