// Common types
export interface LocalizedText {
    en: string;
    fr: string;
}

export interface Price {
    curr: string;
    value: number;
}

export interface Dimension {
    height: number;
    width: number;
}

export interface LifeDates {
    date_of_birth: string;
    date_of_death: string;
}

export interface EvolutionItem {
    _id: string;
    currency: string;
    totalValue: number;
}

// Artist interface
export interface Artist {
    _id: string;
    name: string;
    description?: LocalizedText;
    life_dates?: LifeDates;
    image?: string;
    bio?: string;
    nationality?: string;
    ic_artist_usd?: number;
    ic_artist_euro?: number;
    evolution?: Array<{ year: string; value: number }>;
    index_artist?: Array<{ year: string; price: Array<{ curr: string; value: number }> }>;
    metrics?: Array<{ total_auction_sales?: number; lots_sold?: number; lots_unsold?: number; all_time_record_price?: string }>;
}

// Document properties interface
export interface DocumentProperties {
    auth_certif_en?: string;
    auth_certif_fr?: string;
    client_pdf_en?: string;
    client_pdf_fr?: string;
    notice_en?: string;
    notice_fr?: string;
    property_title_en?: string;
    property_title_fr?: string;
    technical_file_en?: string;
    technical_file_fr?: string;
}

// Image properties interface
export interface ImageProperties {
    pr_certif_img?: string;
    pr_checkout_image?: string;
    pr_cover_img_hashed?: string;
    pr_cover_img?: string;
    preview_img?: string;
}

// Main painting interface
export interface PaintingData extends DocumentProperties, ImageProperties {
    _id: string;
    title: LocalizedText;
    painting_type_en: string;
    painting_type_fr: string;
    description: LocalizedText;
    price: Price[];
    dimension: Dimension;
    market: number;
    end_date: string;
    like_options: LocalizedText[];
    images: string[];
    cover_img: string;
    year: string;
    comp_tables: Array<{
        img: string;
        painting_en: string;
        painting_fr: string;
        year: string;
        all_time_record_usd: string;
        all_time_record_euro: string;
        height: string;
        width: string;
    }>;
    purchase_price_per_part: Price;
    price_per_part: Price;
    evolution_list: EvolutionItem[];
    total_part: number;
    sold_out_part: number;
    artist: Artist;
    artist_evolution?: Array<{
        year: string;
        value: number;
    }>;
    index_artist?: Array<{
        year: string;
        price: Array<{
            curr: string;
            value: number;
        }>;
    }>;
    artist_metrics?: {
        total_auction_sales?: number;
        lots_sold?: number;
        lots_unsold?: number;
        turnover_2023?: number;
    };
}

// API Response interface
export interface ApiResponse {
    paintings: PaintingData[];
    base_url: string;
    count?: number;
    next?: string | null;
    previous?: string | null;
}

