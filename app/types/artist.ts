export interface Price {
    curr: string;
    value: number;
}

export interface Metric {
    year: string;
    lots_sold: number;
    lots_unsold: number;
    all_time_record_price: Price[];
    total_auction_sales: Price[];
}

export interface IndexArtist {
    year: string;
    price: Price[];
}

export interface CompTable {
    img: string;
    painting_en: string;
    painting_fr: string;
    first_year: string;
    first_year_value_usd: string;
    first_year_value_euro: string;
    last_year: string;
    last_year_value_usd: string;
    last_year_value_euro: string;
    year: string;
    height: string;
    width: string;
    percentage: string;
    direction: 'up' | 'down';
}

export interface Evolution {
    _id: string;
    currency: string;
    totalValue: number;
}

export interface Artist {
    _id: string;
    name: string;
    description: {
        en: string;
        fr: string;
    };
    life_dates: {
        date_of_birth: string;
        date_of_death: string;
    };
    image: string;
    metrics: Metric[];
    ic_artist_usd: number;
    ic_artist_euro: number;
    index_artist: IndexArtist[];
    comp_tables: CompTable[];
    ic_direction: 'up' | 'down';
    ic_percentage: number;
    evolution_list: Evolution[];
    paintings: CompTable[]; 
}

export interface ApiResponse {
    base_url: string;
    artists: Artist[];
}
