// Chart configuration
export const CHART_CONFIG = {
  height: 256,
  strokeWidth: 2,
  dotRadius: 3,
  activeDotRadius: 5,
  strokeColor: '#1f2937',
  gridColor: '#f0f0f0',
  axisColor: '#888',
  fontSize: 11,
} as const;

// Image sizes configuration
export const IMAGE_SIZES = {
  artwork: { width: 343, height: 401 },
  certificate: { width: 441, height: 517 },
  artist: { width: 60, height: 60 },
  documentIcon: { width: 40, height: 40 },
} as const;

// Typography configuration for Figma specs
export const TYPOGRAPHY = {
  title: {
    fontSize: '25px',
    lineHeight: '35px',
    fontFamily: 'Prata',
    fontWeight: 'normal',
  },
  certificateTitle: {
    fontSize: '40px',
    lineHeight: '56px',
    fontFamily: 'Prata',
    fontWeight: 400,
    width: '290px',
    height: '56px',
  },
  argumentsTitle: {
    fontSize: '40px',
    lineHeight: '56px',
    fontFamily: 'Prata',
    fontWeight: 400,
    width: '290px',
    height: '56px',
  },
  priceMatrix: {
    fontSize: '16px',
    lineHeight: '40px',
    fontFamily: 'Karla',
    fontWeight: 700,
    width: '196px',
    height: '28px',
  },
  documentLabel: {
    fontSize: '14px',
    lineHeight: '20px',
    fontFamily: 'Karla',
    fontWeight: 700,
    width: '89px',
    height: '40px',
  },
  artistName: {
    fontSize: '20px',
    lineHeight: '28px',
    fontFamily: 'Karla',
    fontWeight: 700,
    width: '200px',
    height: '28px',
  },
  artistDates: {
    fontSize: '14px',
    lineHeight: '24px',
    fontFamily: 'Karla',
    fontWeight: 400,
    width: '76px',
    height: '24px',
  },
  artworkDetailsContainer: {
    width: '264px',
    fontSize: '12px',
    lineHeight: '18px',
  },
  artworkDetailsTitle: {
    fontSize: '28px',
    fontWeight: 700,
    lineHeight: '30px',
    width: '302px',
    height: '30px',
  },
  artworkDetailsLifeDates: {
    fontSize: '16px',
    fontWeight: 400,
    lineHeight: '30px',
  },
  artworkDetailsInfo: {
    width: '151px',
    height: '54px',
    fontSize: '12px',
    lineHeight: '18px',
  },
  argumentsNumber: {
    fontSize: '40px',
    fontWeight: 400,
    fontFamily: 'Prata',
    lineHeight: '40px',
    width: '42px',
    height: '40px',
    color: '#b38642',
  },
  argumentsText: {
    fontSize: '14px',
    lineHeight: '20px',
    fontFamily: 'Karla',
    color: '#374151',
  },
  artworkSectionTitle: {
    fontSize: '48px',
    fontWeight: 400,
    fontFamily: 'Prata',
    lineHeight: '56px',
    color: '#111827',
  },
  artworkDescription: {
    fontSize: '16px',
    fontWeight: 400,
    fontFamily: 'Karla',
    lineHeight: '26px',
    color: '#374151',
  },
  artworkArtistName: {
    fontSize: '20px',
    fontWeight: 700,
    fontFamily: 'Karla',
    lineHeight: '24px',
    color: '#111827',
  },
  artworkArtistDates: {
    fontSize: '16px',
    fontWeight: 400,
    fontFamily: 'Karla',
    lineHeight: '20px',
    color: '#6B7280',
  },
  artworkTechnicalInfo: {
    fontSize: '14px',
    fontWeight: 600,
    fontFamily: 'Karla',
    lineHeight: '18px',
    color: '#374151',
  },
  artworkSectionLayout: {
    width: '1600px',
    height: '587px',
    backgroundColor: '#F5F5F5',
  },
  artworkSectionImage: {
    width: '509px',
    height: '282px',
  },
  sharesAvailableText: {
    fontSize: '18px',
    fontWeight: 500,
    fontFamily: 'Karla',
    lineHeight: '24px',
    color: '#111827',
  },
  sharesAvailableNumber: {
    fontSize: '18px',
    fontWeight: 600,
    fontFamily: 'Karla',
    lineHeight: '24px',
    color: '#B8860B',
  },
  sharesProgressBar: {
    height: '32px',
    borderRadius: '2px',
  },
  sharesProgressText: {
    fontSize: '14px',
    fontWeight: 500,
    fontFamily: 'Karla',
    lineHeight: '16px',
    color: '#FFFFFF',
  },
  sharesInvestorsText: {
    fontSize: '18px',
    fontWeight: 500,
    fontFamily: 'Karla',
    lineHeight: '24px',
    color: '#B8860B',
  },
  artistSectionName: {
    fontSize: '20px',
    fontWeight: 600,
    fontFamily: 'Karla',
    lineHeight: '32px',
    color: '#111827',
  },
  artistSectionDescription: {
    fontSize: '16px',
    fontWeight: 400,
    fontFamily: 'Karla',
    lineHeight: '24px',
    color: '#374151',
  },
  artistSectionTitle: {
    fontSize: '48px',
    fontWeight: 400,
    fontFamily: 'Prata',
    lineHeight: '56px',
    color: '#111827',
  },
  artistIndexTitle: {
    fontSize: '32px',
    fontWeight: 400,
    fontFamily: 'Prata',
    lineHeight: '40px',
    color: '#111827',
  },
  artistIndexSubtitle: {
    fontSize: '16px',
    fontWeight: 400,
    fontFamily: 'Karla',
    lineHeight: '24px',
    color: '#6B7280',
  },
} as const;

// Button configuration
export const BUTTON_CONFIG = {
  buy: {
    height: '54px',
    paddingTop: '12px',
    paddingRight: '30px',
    paddingBottom: '12px',
    paddingLeft: '30px',
    gap: '8px',
    borderWidth: '1px',
  },
} as const;

// Color configuration
export const COLORS = {
  primary: 'var(--color-primary)',
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    500: '#6b7280',
    600: '#4b5563',
    800: '#1f2937',
    900: '#111827',
  },
} as const;

// Document types for icons
export const DOCUMENT_TYPES = {
  PROSPECTUS: 'prospectus',
  TECHNICAL: 'technical',
  AUTHENTICATION: 'authentication',
  PROPERTY_TITLE: 'property_title',
  NOTICE: 'notice',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  PRIMARY_MARKET: '/paintings/?type=primary&limit=1&offset=0&currency=USD',
} as const;
