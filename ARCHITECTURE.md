# Project Architecture and Conventions

This document outlines the key architectural patterns and conventions for the WeArt frontend application. Following these guidelines will help maintain consistency and scalability.

## 1. Page and Component Structure

The project is a Next.js application using the App Router.

-   **Route Definitions**: Page routes are defined in `app/(main)/...` for the default language (English) and `app/fr/...` for French. These page files (e.g., `app/(main)/artists/page.tsx`) should be lightweight `async` Server Components responsible for fetching data and dictionaries.

-   **Page Components**: The actual UI and logic for a page are encapsulated in dedicated components located in `app/components/pages/`. For example, the `/artists` route is rendered by `app/components/pages/ArtistsPage.tsx`.

-   **Client Components**: Components requiring interactivity, state, or lifecycle hooks (e.g., forms, interactive charts) must be marked with `"use client"`. They should receive data and translations as props from their parent Server Component whenever possible.

## 2. Internationalization (i18n)

The application is designed to be multilingual.

-   **Language Directories**: The project uses separate directory trees for each language: `app/(main)` for English (default) and `app/fr` for French.

-   **Translated Slugs**: URL paths should be translated to provide a fully localized experience (e.g., `/dashboard` in English becomes `/tableau-de-bord` in French).

-   **Dictionary**: All user-facing text is managed in JSON files located in `app/locales/` (`en.json`, `fr.json`).

-   **Translation Loading**: The `app/lib/get-translation.ts` file provides a `getDictionary` function to load the appropriate dictionary in Server Components. The `Dictionary` type is automatically inferred from `en.json`, so any new keys must be added there first.

## 3. API Usage

-   **API Endpoint**: All API calls, whether client-side or server-side, must use the base URL stored in the `process.env.NEXT_PUBLIC_API_BASE_URL` environment variable.

    ```javascript
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/your-endpoint`);
    ```

-   **Data Fetching**: Data should primarily be fetched within `async` Server Components before the page is rendered. This improves performance by leveraging server-side rendering.
