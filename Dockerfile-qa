# Use Node 20 for building
FROM node:20-alpine AS builder

WORKDIR /app

# Install dependencies
COPY package*.json ./


RUN npm install

# Copy source and build Next.js app
COPY . .
RUN cp k8s/qa/.env-qa.local .env.local
RUN npm run build

# Production image
FROM node:20-alpine AS runner

# Install pm2
RUN npm install -g pm2

# Create app directory
WORKDIR /app

# Copy built app from builder
COPY --from=builder /app /app

# Copy PM2 config
COPY k8s/ecosystem.config.js .


# Install nginx
RUN apk add --no-cache nginx

# Copy nginx config
COPY k8s/nginx.conf /etc/nginx/nginx.conf

# Make sure Nginx logs go to stdout/stderr for Kubernetes logging
RUN ln -sf /dev/stdout /var/log/nginx/access.log && \
    ln -sf /dev/stderr /var/log/nginx/error.log
# Expose ports: 80 (nginx) and 3000 (Next.js default)
EXPOSE 80

# Start both nginx and Next.js via PM2
# Start both nginx and app via a simple supervisor script
CMD ["sh", "-c", "nginx -g 'daemon off;' & pm2-runtime ecosystem.config.js"]
